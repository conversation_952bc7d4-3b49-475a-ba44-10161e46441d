import { ApiResponse } from "./common";

export enum PackageReservationStatus {
  INQUIRY = "INQUIRY",
  PENDING = "PENDING",
  CONFIRMED = "CONFIRMED",
  PARTIALLY_PAID = "PARTIALLY_PAID",
  PAID = "PAID",
  IN_PROGRESS = "IN_PROGRESS",
  COMPLETED = "COMPLETED",
  CANCELLED = "CANCELLED",
  NO_SHOW = "NO_SHOW",
  REFUNDED = "REFUNDED",
}

export enum PaymentStatus {
  PENDING = "PENDING",
  PARTIAL = "PARTIAL",
  PAID = "PAID",
  REFUNDED = "REFUNDED",
  CANCELLED = "CANCELLED",
}

export enum ReservationSource {
  ONLINE = "ONLINE",
  PHONE = "PHONE",
  WALK_IN = "WALK_IN",
  EMAIL = "EMAIL",
  AGENT = "AGENT",
  CORPORATE = "CORPORATE",
  REPEAT_GUEST = "REPEAT_GUEST",
  REFERRAL = "REFERRAL",
  OTHER = "OTHER",
}

export enum TaxType {
  INCLUSIVE = "INCLUSIVE",
  EXCLUSIVE = "EXCLUSIVE",
  OUT_OF_SCOPE = "OUT_OF_SCOPE",
}

export enum DiscountType {
  PERCENTAGE = "PERCENTAGE",
  FIXED_AMOUNT = "FIXED_AMOUNT",
}

// Backend DTO: PackageReservationDto
export interface PackageReservationDto {
  id: string;
  businessId: string;
  reservationNumber: string;
  referenceNumber?: string;
  packageId: string;
  package: {
    id: string;
    name: string;
    packageCode?: string;
    packageType?: string;
    description?: string;
  };
  startDate: Date;
  endDate: Date;
  totalNumberOfGuests: number;
  numberOfAdults: number;
  numberOfChildren: number;
  status: PackageReservationStatus;
  reservationSource?: ReservationSource;
  paymentStatus: PaymentStatus;
  subtotal: string;
  total: string;
  depositPaid?: string;
  balanceDue?: string;
  discountType?: DiscountType;
  discountValue?: string;
  discountAmount?: string;
  taxType: TaxType;
  taxAmount?: string;
  notes?: string;
  cancellationReason?: string;
  cancellationDate?: Date;
  confirmationSent: boolean;
  confirmationSentAt?: Date;
  reminderSent: boolean;
  reminderSentAt?: Date;
  guests: PackageReservationGuestDto[];
  createdBy: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Backend DTO: CreatePackageReservationDto
export interface CreatePackageReservationDto {
  reservationNumber: string;
  referenceNumber?: string;
  packageId: string;
  startDate: string;
  endDate: string;
  totalNumberOfGuests?: number;
  numberOfAdults: number;
  numberOfChildren?: number;
  status?: PackageReservationStatus;
  reservationSource?: ReservationSource;
  paymentStatus?: PaymentStatus;
  subtotal?: string;
  total?: string;
  depositPaid?: string;
  balanceDue?: string;
  discountType?: DiscountType;
  discountValue?: string;
  discountAmount?: string;
  taxType?: TaxType;
  taxAmount?: string;
  notes?: string;
  cancellationReason?: string;
  cancellationDate?: string;
  confirmationSent?: boolean;
  confirmationSentAt?: string;
  reminderSent?: boolean;
  reminderSentAt?: string;
  guests?: CreatePackageReservationGuestDto[];
}

// Backend DTO: UpdatePackageReservationDto
export interface UpdatePackageReservationDto {
  reservationNumber?: string;
  referenceNumber?: string;
  packageId?: string;
  startDate?: string;
  endDate?: string;
  totalNumberOfGuests?: number;
  numberOfAdults?: number;
  numberOfChildren?: number;
  status?: PackageReservationStatus;
  reservationSource?: ReservationSource;
  paymentStatus?: PaymentStatus;
  subtotal?: string;
  total?: string;
  depositPaid?: string;
  balanceDue?: string;
  discountType?: DiscountType;
  discountValue?: string;
  discountAmount?: string;
  taxType?: TaxType;
  taxAmount?: string;
  notes?: string;
  cancellationReason?: string;
  cancellationDate?: string;
  confirmationSent?: boolean;
  confirmationSentAt?: string;
  reminderSent?: boolean;
  reminderSentAt?: string;
  guests?: CreatePackageReservationGuestDto[];
}

// Backend DTO: PackageReservationSlimDto
export interface PackageReservationSlimDto {
  id: string;
  reservationNumber: string;
  referenceNumber?: string;
  packageName: string;
  startDate: Date;
  endDate: Date;
  totalNumberOfGuests: number;
  status: PackageReservationStatus;
  total: string;
  primaryGuestName?: string;
}

// Guest DTOs
export interface PackageReservationGuestDto {
  id: string;
  guestId: string;
  reservationId: string;
  isPrimaryGuest: boolean;
  primaryGuestId?: string;
  relationshipToPrimary?: string;
  participationStatus: string;
  packageStarted: boolean;
  packageCompleted: boolean;
  notes?: string;
  guest: {
    id: string;
    firstName: string;
    lastName: string;
    email?: string;
    phone?: string;
    dateOfBirth?: Date;
    nationality?: string;
    identificationType?: string;
    identificationNumber?: string;
  };
}

export interface CreatePackageReservationGuestDto {
  guestId?: string;
  isPrimaryGuest?: boolean;
  primaryGuestId?: string;
  relationshipToPrimary?: string;
  participationStatus?: string;
  notes?: string;
  // Guest creation fields if guestId is not provided
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  dateOfBirth?: string;
  nationality?: string;
  identificationType?: string;
  identificationNumber?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
}

// Backend DTO: PaginatedPackageReservationsResponseDto
export interface PaginationMetaDto {
  total: number;
  page: number;
  totalPages: number;
}

export interface PaginatedPackageReservationsResponseDto {
  data: PackageReservationTableData[];
  meta: PaginationMetaDto;
}

// Backend DTO: DeletePackageReservationResponseDto
export interface DeletePackageReservationResponseDto {
  success: boolean;
  message: string;
}

// Backend DTO: PackageReservationIdResponseDto
export interface PackageReservationIdResponseDto {
  id: string;
}

// Backend DTO: BulkPackageReservationIdsResponseDto
export interface BulkPackageReservationIdsResponseDto {
  ids: string[];
}

// Backend DTO: BulkDeletePackageReservationDto
export interface BulkDeletePackageReservationDto {
  packageReservationIds: string[];
}

// Backend DTO: BulkDeletePackageReservationResponseDto
export interface BulkDeletePackageReservationResponseDto {
  deleted: number;
  message: string;
  deletedIds: string[];
}

// Backend DTO: BulkUpdatePackageReservationStatusDto
export interface BulkUpdatePackageReservationStatusDto {
  packageReservationIds: string[];
  status: PackageReservationStatus;
}

// Backend DTO: BulkUpdatePackageReservationStatusResponseDto
export interface BulkUpdatePackageReservationStatusResponseDto {
  updated: number;
  message: string;
  updatedIds: string[];
  failed?: Array<{
    packageReservationId: string;
    error: string;
  }>;
}

// API Response wrappers
export interface PackageReservationResponse extends ApiResponse<PackageReservationDto> {}

export interface PackageReservationTableResponse
  extends ApiResponse<PackageReservationTableData[]> {}

export interface PackageReservationPaginatedResponse
  extends ApiResponse<PaginatedPackageReservationsResponseDto | null> {}

export interface SimplePackageReservationResponse
  extends ApiResponse<PackageReservationSlimDto[]> {}

export interface PackageReservationNumberAvailabilityResponse
  extends ApiResponse<{ available: boolean }> {}

export interface PackageReservationIdResponse
  extends ApiResponse<PackageReservationIdResponseDto> {}

export interface BulkPackageReservationIdsResponse
  extends ApiResponse<BulkPackageReservationIdsResponseDto> {}

export interface BulkDeletePackageReservationResponse
  extends ApiResponse<BulkDeletePackageReservationResponseDto> {}

export interface BulkUpdatePackageReservationStatusResponse
  extends ApiResponse<BulkUpdatePackageReservationStatusResponseDto> {}

// Table data interface - optimized for table display
export interface PackageReservationTableData {
  id: string;
  reservationNumber: string;
  referenceNumber?: string;
  packageId: string;
  packageName: string;
  startDate: Date;
  endDate: Date;
  totalNumberOfGuests: number;
  numberOfAdults: number;
  numberOfChildren: number;
  status: PackageReservationStatus;
  paymentStatus: PaymentStatus;
  total: string;
  primaryGuestName?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Form values interface for UI components
export interface PackageReservationFormValues {
  reservationNumber: string;
  referenceNumber?: string;
  packageId: string;
  startDate: string;
  endDate: string;
  numberOfAdults: number;
  numberOfChildren?: number;
  status?: PackageReservationStatus;
  reservationSource?: ReservationSource;
  paymentStatus?: PaymentStatus;
  discountType?: DiscountType;
  discountValue?: string;
  taxType?: TaxType;
  notes?: string;
  guests?: CreatePackageReservationGuestDto[];
}
