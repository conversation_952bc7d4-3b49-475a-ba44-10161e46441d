import {
  PackageReservationResponse,
  PackageReservationPaginatedResponse,
  PackageReservationIdResponse,
  BulkPackageReservationIdsResponse,
  BulkDeletePackageReservationDto,
  BulkDeletePackageReservationResponse,
  PackageReservationNumberAvailabilityResponse,
  SimplePackageReservationResponse,
  CreatePackageReservationDto,
  UpdatePackageReservationDto,
  PackageReservationStatus,
  BulkUpdatePackageReservationStatusDto,
  BulkUpdatePackageReservationStatusResponse,
} from "@/types/package-reservation";
import { ApiResponse, ApiStatus } from "@/types/common";
import { GetPackageReservationsSchema } from "./validations";
import axios from "@/utils/axios";

// Create a new package reservation
export async function createPackageReservationApi(
  data: CreatePackageReservationDto
): Promise<PackageReservationIdResponse> {
  try {
    const res = await axios.post("/package-reservations", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk create package reservations
export async function bulkCreatePackageReservationsApi(
  packageReservations: CreatePackageReservationDto[]
): Promise<BulkPackageReservationIdsResponse> {
  try {
    const res = await axios.post("/package-reservations/bulk", {
      packageReservations,
    });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get package reservations with pagination and filters
export async function getPackageReservationsApi(
  params: GetPackageReservationsSchema
): Promise<PackageReservationPaginatedResponse> {
  try {
    const searchParams = new URLSearchParams();

    if (params.page) searchParams.append("page", String(params.page));
    if (params.perPage) searchParams.append("limit", String(params.perPage));
    if (params.from) searchParams.append("from", params.from);
    if (params.to) searchParams.append("to", params.to);
    if (params.reservationNumber)
      searchParams.append("reservationNumber", params.reservationNumber);
    if (params.referenceNumber)
      searchParams.append("referenceNumber", params.referenceNumber);
    if (params.packageId) searchParams.append("packageId", params.packageId);
    if (params.status) searchParams.append("status", params.status);
    if (params.paymentStatus)
      searchParams.append("paymentStatus", params.paymentStatus);
    if (params.filters && params.filters.length > 0) {
      searchParams.append("filters", JSON.stringify(params.filters));
    }
    if (params.joinOperator)
      searchParams.append("joinOperator", params.joinOperator);
    if (params.sort && params.sort.length > 0) {
      searchParams.append("sort", JSON.stringify(params.sort));
    }

    const res = await axios.get(
      `/package-reservations?${searchParams.toString()}`
    );
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Check package reservation number availability
export async function checkPackageReservationNumberAvailabilityApi(
  reservationNumber: string
): Promise<PackageReservationNumberAvailabilityResponse> {
  try {
    const res = await axios.get(
      `/package-reservations/check-reservation-number-availability?reservationNumber=${encodeURIComponent(
        reservationNumber
      )}`
    );
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Check package reference number availability
export async function checkPackageReferenceNumberAvailabilityApi(
  referenceNumber: string
): Promise<PackageReservationNumberAvailabilityResponse> {
  try {
    const res = await axios.get(
      `/package-reservations/check-reference-number-availability?referenceNumber=${encodeURIComponent(
        referenceNumber
      )}`
    );
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get package reservations in slim format (for dropdowns/selects)
export async function getPackageReservationsSlimApi(): Promise<SimplePackageReservationResponse> {
  try {
    const res = await axios.get("/package-reservations/slim");
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get a single package reservation by ID
export async function getPackageReservationApi(
  id: string
): Promise<PackageReservationResponse> {
  try {
    const res = await axios.get(`/package-reservations/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Update a package reservation
export async function updatePackageReservationApi(
  id: string,
  data: UpdatePackageReservationDto
): Promise<PackageReservationIdResponse> {
  try {
    const res = await axios.patch(`/package-reservations/${id}`, data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Delete a package reservation
export async function deletePackageReservationApi(
  id: string
): Promise<ApiResponse<{ success: boolean; message: string }>> {
  try {
    const res = await axios.delete(`/package-reservations/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk delete package reservations
export async function bulkDeletePackageReservationsApi(
  packageReservationIds: string[]
): Promise<BulkDeletePackageReservationResponse> {
  try {
    const data: BulkDeletePackageReservationDto = { packageReservationIds };
    const res = await axios.delete("/package-reservations/bulk", { data });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Update package reservation status (convenience function)
export async function updatePackageReservationStatusApi(
  id: string,
  status: PackageReservationStatus
): Promise<PackageReservationIdResponse> {
  try {
    const res = await axios.patch(`/package-reservations/${id}`, { status });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk update package reservation status
export async function bulkUpdatePackageReservationStatusApi(
  packageReservationIds: string[],
  status: PackageReservationStatus
): Promise<BulkUpdatePackageReservationStatusResponse> {
  try {
    const res = await axios.patch("/package-reservations/bulk-status", {
      packageReservationIds,
      status,
    });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Check package availability
export async function checkPackageAvailabilityApi(data: {
  startDate: string;
  endDate: string;
  adults: number;
  children?: number;
  packageIds?: string[];
}): Promise<ApiResponse<any>> {
  try {
    const res = await axios.post("/package-reservations/check-availability", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Calculate package pricing
export async function calculatePackagePricingApi(data: {
  packageId: string;
  startDate: string;
  endDate: string;
  adults: number;
  children?: number;
  discountType?: string;
  discountValue?: string;
  taxType?: string;
}): Promise<ApiResponse<any>> {
  try {
    const res = await axios.post("/package-reservations/calculate-pricing", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Confirm package reservation
export async function confirmPackageReservationApi(
  id: string,
  data?: any
): Promise<ApiResponse<any>> {
  try {
    const res = await axios.patch(`/package-reservations/${id}/confirm`, data || {});
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Cancel package reservation
export async function cancelPackageReservationApi(
  id: string,
  data: { cancellationReason?: string }
): Promise<ApiResponse<any>> {
  try {
    const res = await axios.patch(`/package-reservations/${id}/cancel`, data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Send confirmation
export async function sendPackageConfirmationApi(
  id: string,
  data?: any
): Promise<ApiResponse<any>> {
  try {
    const res = await axios.post(`/package-reservations/${id}/send-confirmation`, data || {});
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Send reminder
export async function sendPackageReminderApi(
  id: string,
  data?: any
): Promise<ApiResponse<any>> {
  try {
    const res = await axios.post(`/package-reservations/${id}/send-reminder`, data || {});
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}
