import {
  PackageReservationPaginatedResponse,
  PackageReservationIdResponse,
  BulkPackageReservationIdsResponse,
  BulkDeletePackageReservationResponse,
  PackageReservationNumberAvailabilityResponse,
  SimplePackageReservationResponse,
  PackageReservationResponse,
  CreatePackageReservationDto,
  UpdatePackageReservationDto,
  PackageReservationStatus,
  BulkUpdatePackageReservationStatusResponse,
  PackageReservationTableData,
} from "@/types/package-reservation";
import { ApiResponse, ApiStatus } from "@/types/common";
import {
  GetPackageReservationsSchema,
  CreatePackageReservationSchema,
  UpdatePackageReservationSchema,
  BulkCreatePackageReservationsSchema,
  CheckPackageAvailabilitySchema,
  CalculatePackagePricingSchema,
} from "./validations";

// Real API imports
import {
  createPackageReservationApi,
  bulkCreatePackageReservationsApi,
  getPackageReservationsApi,
  checkPackageReservationNumberAvailabilityApi,
  checkPackageReferenceNumberAvailabilityApi,
  getPackageReservationsSlimApi,
  getPackageReservationApi,
  updatePackageReservationApi,
  deletePackageReservationApi,
  bulkDeletePackageReservationsApi,
  updatePackageReservationStatusApi,
  bulkUpdatePackageReservationStatusApi,
  checkPackageAvailabilityApi,
  calculatePackagePricingApi,
  confirmPackageReservationApi,
  cancelPackageReservationApi,
  sendPackageConfirmationApi,
  sendPackageReminderApi,
} from "./api";

// Demo API imports
import {
  createPackageReservationDemo,
  bulkCreatePackageReservationsDemo,
  getPackageReservationsTableDataDemo,
  checkPackageReservationNumberAvailabilityDemo,
  checkPackageReferenceNumberAvailabilityDemo,
  getPackageReservationsSlimDemo,
  getPackageReservationDemo,
  updatePackageReservationDemo,
  deletePackageReservationDemo,
  bulkDeletePackageReservationsDemo,
  updatePackageReservationStatusDemo,
  bulkUpdatePackageReservationStatusDemo,
} from "./demo";

// Environment check
const USE_DEMO = process.env.NODE_ENV === "development" && process.env.NEXT_PUBLIC_USE_DEMO_DATA === "true";

// Create package reservation
export async function createPackageReservation(
  data: CreatePackageReservationSchema
): Promise<PackageReservationIdResponse> {
  if (USE_DEMO) {
    return createPackageReservationDemo(data);
  }
  return createPackageReservationApi(data);
}

// Bulk create package reservations
export async function bulkCreatePackageReservations(
  packageReservations: CreatePackageReservationDto[]
): Promise<BulkPackageReservationIdsResponse> {
  if (USE_DEMO) {
    return bulkCreatePackageReservationsDemo(packageReservations);
  }
  return bulkCreatePackageReservationsApi(packageReservations);
}

// Get package reservations table data
export async function getPackageReservationsTableData(
  params: GetPackageReservationsSchema
): Promise<PackageReservationPaginatedResponse> {
  if (USE_DEMO) {
    return getPackageReservationsTableDataDemo(params);
  }
  return getPackageReservationsApi(params);
}

// Check package reservation number availability
export async function checkPackageReservationNumberAvailability(
  reservationNumber: string
): Promise<PackageReservationNumberAvailabilityResponse> {
  if (USE_DEMO) {
    return checkPackageReservationNumberAvailabilityDemo(reservationNumber);
  }
  return checkPackageReservationNumberAvailabilityApi(reservationNumber);
}

// Check package reference number availability
export async function checkPackageReferenceNumberAvailability(
  referenceNumber: string
): Promise<PackageReservationNumberAvailabilityResponse> {
  if (USE_DEMO) {
    return checkPackageReferenceNumberAvailabilityDemo(referenceNumber);
  }
  return checkPackageReferenceNumberAvailabilityApi(referenceNumber);
}

// Get package reservations slim
export async function getPackageReservationsSlim(): Promise<SimplePackageReservationResponse> {
  if (USE_DEMO) {
    return getPackageReservationsSlimDemo();
  }
  return getPackageReservationsSlimApi();
}

// Get single package reservation
export async function getPackageReservation(id: string): Promise<PackageReservationResponse> {
  if (USE_DEMO) {
    return getPackageReservationDemo(id);
  }
  return getPackageReservationApi(id);
}

// Update package reservation
export async function updatePackageReservation(
  id: string,
  data: UpdatePackageReservationSchema
): Promise<PackageReservationIdResponse> {
  if (USE_DEMO) {
    return updatePackageReservationDemo(id, data);
  }
  return updatePackageReservationApi(id, data);
}

// Delete package reservation
export async function deletePackageReservation(
  id: string
): Promise<ApiResponse<{ success: boolean; message: string }>> {
  if (USE_DEMO) {
    return deletePackageReservationDemo(id);
  }
  return deletePackageReservationApi(id);
}

// Bulk delete package reservations
export async function bulkDeletePackageReservations(
  packageReservationIds: string[]
): Promise<BulkDeletePackageReservationResponse> {
  if (USE_DEMO) {
    return bulkDeletePackageReservationsDemo(packageReservationIds);
  }
  return bulkDeletePackageReservationsApi(packageReservationIds);
}

// Update package reservation status
export async function updatePackageReservationStatus(
  id: string,
  status: PackageReservationStatus
): Promise<PackageReservationIdResponse> {
  if (USE_DEMO) {
    return updatePackageReservationStatusDemo(id, status);
  }
  return updatePackageReservationStatusApi(id, status);
}

// Bulk update package reservation status
export async function bulkUpdatePackageReservationStatus(
  packageReservationIds: string[],
  status: PackageReservationStatus
): Promise<BulkUpdatePackageReservationStatusResponse> {
  if (USE_DEMO) {
    return bulkUpdatePackageReservationStatusDemo(packageReservationIds, status);
  }
  return bulkUpdatePackageReservationStatusApi(packageReservationIds, status);
}

// Check package availability
export async function checkPackageAvailability(
  data: CheckPackageAvailabilitySchema
): Promise<ApiResponse<any>> {
  // Always use real API for availability checks
  return checkPackageAvailabilityApi(data);
}

// Calculate package pricing
export async function calculatePackagePricing(
  data: CalculatePackagePricingSchema
): Promise<ApiResponse<any>> {
  // Always use real API for pricing calculations
  return calculatePackagePricingApi(data);
}

// Confirm package reservation
export async function confirmPackageReservation(
  id: string,
  data?: any
): Promise<ApiResponse<any>> {
  // Always use real API for confirmation
  return confirmPackageReservationApi(id, data);
}

// Cancel package reservation
export async function cancelPackageReservation(
  id: string,
  data: { cancellationReason?: string }
): Promise<ApiResponse<any>> {
  // Always use real API for cancellation
  return cancelPackageReservationApi(id, data);
}

// Send package confirmation
export async function sendPackageConfirmation(
  id: string,
  data?: any
): Promise<ApiResponse<any>> {
  // Always use real API for sending confirmation
  return sendPackageConfirmationApi(id, data);
}

// Send package reminder
export async function sendPackageReminder(
  id: string,
  data?: any
): Promise<ApiResponse<any>> {
  // Always use real API for sending reminder
  return sendPackageReminderApi(id, data);
}

// Utility functions for data transformation
export function transformPackageReservationForTable(
  reservation: any
): PackageReservationTableData {
  return {
    id: reservation.id,
    reservationNumber: reservation.reservationNumber,
    referenceNumber: reservation.referenceNumber,
    packageId: reservation.packageId,
    packageName: reservation.package?.name || reservation.packageName,
    startDate: new Date(reservation.startDate),
    endDate: new Date(reservation.endDate),
    totalNumberOfGuests: reservation.totalNumberOfGuests,
    numberOfAdults: reservation.numberOfAdults,
    numberOfChildren: reservation.numberOfChildren,
    status: reservation.status,
    paymentStatus: reservation.paymentStatus,
    total: reservation.total,
    primaryGuestName: reservation.primaryGuestName || 
      reservation.guests?.find((g: any) => g.isPrimaryGuest)?.guest?.firstName + " " +
      reservation.guests?.find((g: any) => g.isPrimaryGuest)?.guest?.lastName,
    createdAt: new Date(reservation.createdAt),
    updatedAt: new Date(reservation.updatedAt),
  };
}

// Helper function to format currency
export function formatCurrency(amount: string | number): string {
  const num = typeof amount === "string" ? parseFloat(amount) : amount;
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
  }).format(num);
}

// Helper function to format date range
export function formatDateRange(startDate: Date, endDate: Date): string {
  const start = startDate.toLocaleDateString();
  const end = endDate.toLocaleDateString();
  return `${start} - ${end}`;
}

// Helper function to calculate duration
export function calculateDuration(startDate: Date, endDate: Date): number {
  const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
}
