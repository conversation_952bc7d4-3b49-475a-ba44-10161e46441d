import {
  PackageReservationDto,
  PackageReservationSlimDto,
  PackageReservationTableData,
  PackageReservationStatus,
  PaymentStatus,
  ReservationSource,
  TaxType,
  DiscountType,
  PackageReservationPaginatedResponse,
  PackageReservationIdResponse,
  BulkPackageReservationIdsResponse,
  BulkDeletePackageReservationResponse,
  PackageReservationNumberAvailabilityResponse,
  SimplePackageReservationResponse,
  PackageReservationResponse,
  CreatePackageReservationDto,
  UpdatePackageReservationDto,
  PaginatedPackageReservationsResponseDto,
  BulkUpdatePackageReservationStatusResponse,
} from "@/types/package-reservation";
import { ApiStatus, ApiResponse } from "@/types/common";
import { GetPackageReservationsSchema } from "./validations";

// Demo package reservations data
const demoPackageReservations: PackageReservationDto[] = [
  {
    id: "pkg-res-001",
    businessId: "business-001",
    reservationNumber: "PKG-2024-001",
    referenceNumber: "REF-001",
    packageId: "pkg-001",
    package: {
      id: "pkg-001",
      name: "Honeymoon Package",
      packageCode: "HONEY-001",
      packageType: "HONEYMOON_PACKAGE",
      description: "Romantic getaway package for couples",
    },
    startDate: new Date("2024-02-14T15:00:00Z"),
    endDate: new Date("2024-02-17T11:00:00Z"),
    totalNumberOfGuests: 2,
    numberOfAdults: 2,
    numberOfChildren: 0,
    status: PackageReservationStatus.CONFIRMED,
    reservationSource: ReservationSource.ONLINE,
    paymentStatus: PaymentStatus.PAID,
    subtotal: "1500.00",
    total: "1650.00",
    depositPaid: "500.00",
    balanceDue: "0.00",
    discountType: DiscountType.PERCENTAGE,
    discountValue: "10",
    discountAmount: "150.00",
    taxType: TaxType.INCLUSIVE,
    taxAmount: "150.00",
    notes: "Anniversary celebration",
    confirmationSent: true,
    confirmationSentAt: new Date("2024-01-15T10:00:00Z"),
    reminderSent: false,
    guests: [
      {
        id: "guest-rel-001",
        guestId: "guest-001",
        reservationId: "pkg-res-001",
        isPrimaryGuest: true,
        participationStatus: "CONFIRMED",
        packageStarted: false,
        packageCompleted: false,
        guest: {
          id: "guest-001",
          firstName: "John",
          lastName: "Doe",
          email: "<EMAIL>",
          phone: "+1234567890",
          dateOfBirth: new Date("1990-01-01"),
          nationality: "US",
          identificationType: "PASSPORT",
          identificationNumber: "P123456789",
        },
      },
      {
        id: "guest-rel-002",
        guestId: "guest-002",
        reservationId: "pkg-res-001",
        isPrimaryGuest: false,
        primaryGuestId: "guest-001",
        relationshipToPrimary: "SPOUSE",
        participationStatus: "CONFIRMED",
        packageStarted: false,
        packageCompleted: false,
        guest: {
          id: "guest-002",
          firstName: "Jane",
          lastName: "Doe",
          email: "<EMAIL>",
          phone: "+1234567891",
          dateOfBirth: new Date("1992-05-15"),
          nationality: "US",
          identificationType: "PASSPORT",
          identificationNumber: "P987654321",
        },
      },
    ],
    createdBy: "Admin User",
    updatedBy: "Admin User",
    createdAt: new Date("2024-01-15T09:00:00Z"),
    updatedAt: new Date("2024-01-15T10:00:00Z"),
  },
  {
    id: "pkg-res-002",
    businessId: "business-001",
    reservationNumber: "PKG-2024-002",
    packageId: "pkg-002",
    package: {
      id: "pkg-002",
      name: "Family Adventure Package",
      packageCode: "FAM-001",
      packageType: "FAMILY_PACKAGE",
      description: "Fun-filled family adventure package",
    },
    startDate: new Date("2024-03-01T14:00:00Z"),
    endDate: new Date("2024-03-05T12:00:00Z"),
    totalNumberOfGuests: 4,
    numberOfAdults: 2,
    numberOfChildren: 2,
    status: PackageReservationStatus.PENDING,
    reservationSource: ReservationSource.PHONE,
    paymentStatus: PaymentStatus.PENDING,
    subtotal: "2400.00",
    total: "2640.00",
    depositPaid: "800.00",
    balanceDue: "1840.00",
    taxType: TaxType.EXCLUSIVE,
    taxAmount: "240.00",
    notes: "Family vacation with kids",
    confirmationSent: false,
    reminderSent: false,
    guests: [
      {
        id: "guest-rel-003",
        guestId: "guest-003",
        reservationId: "pkg-res-002",
        isPrimaryGuest: true,
        participationStatus: "CONFIRMED",
        packageStarted: false,
        packageCompleted: false,
        guest: {
          id: "guest-003",
          firstName: "Michael",
          lastName: "Smith",
          email: "<EMAIL>",
          phone: "+1234567892",
          dateOfBirth: new Date("1985-08-20"),
          nationality: "CA",
          identificationType: "PASSPORT",
          identificationNumber: "C123456789",
        },
      },
    ],
    createdBy: "Admin User",
    createdAt: new Date("2024-01-20T14:30:00Z"),
    updatedAt: new Date("2024-01-20T14:30:00Z"),
  },
  {
    id: "pkg-res-003",
    businessId: "business-001",
    reservationNumber: "PKG-2024-003",
    referenceNumber: "REF-003",
    packageId: "pkg-003",
    package: {
      id: "pkg-003",
      name: "Business Conference Package",
      packageCode: "BIZ-001",
      packageType: "BUSINESS_PACKAGE",
      description: "Professional business conference package",
    },
    startDate: new Date("2024-04-15T09:00:00Z"),
    endDate: new Date("2024-04-17T17:00:00Z"),
    totalNumberOfGuests: 1,
    numberOfAdults: 1,
    numberOfChildren: 0,
    status: PackageReservationStatus.COMPLETED,
    reservationSource: ReservationSource.CORPORATE,
    paymentStatus: PaymentStatus.PAID,
    subtotal: "800.00",
    total: "880.00",
    depositPaid: "880.00",
    balanceDue: "0.00",
    taxType: TaxType.INCLUSIVE,
    taxAmount: "80.00",
    notes: "Corporate booking for conference",
    confirmationSent: true,
    confirmationSentAt: new Date("2024-03-15T08:00:00Z"),
    reminderSent: true,
    reminderSentAt: new Date("2024-04-10T09:00:00Z"),
    guests: [
      {
        id: "guest-rel-004",
        guestId: "guest-004",
        reservationId: "pkg-res-003",
        isPrimaryGuest: true,
        participationStatus: "CONFIRMED",
        packageStarted: true,
        packageCompleted: true,
        guest: {
          id: "guest-004",
          firstName: "Sarah",
          lastName: "Johnson",
          email: "<EMAIL>",
          phone: "+**********",
          dateOfBirth: new Date("1988-12-10"),
          nationality: "US",
          identificationType: "DRIVER_LICENSE",
          identificationNumber: "DL123456789",
        },
      },
    ],
    createdBy: "Admin User",
    createdAt: new Date("2024-03-10T11:00:00Z"),
    updatedAt: new Date("2024-04-17T18:00:00Z"),
  },
];

// Convert to table data format
const demoPackageReservationsTableData: PackageReservationTableData[] =
  demoPackageReservations.map((reservation) => ({
    id: reservation.id,
    reservationNumber: reservation.reservationNumber,
    referenceNumber: reservation.referenceNumber,
    packageId: reservation.packageId,
    packageName: reservation.package.name,
    startDate: reservation.startDate,
    endDate: reservation.endDate,
    totalNumberOfGuests: reservation.totalNumberOfGuests,
    numberOfAdults: reservation.numberOfAdults,
    numberOfChildren: reservation.numberOfChildren,
    status: reservation.status,
    paymentStatus: reservation.paymentStatus,
    total: reservation.total,
    primaryGuestName: reservation.guests.find((g) => g.isPrimaryGuest)
      ? `${reservation.guests.find((g) => g.isPrimaryGuest)?.guest.firstName} ${
          reservation.guests.find((g) => g.isPrimaryGuest)?.guest.lastName
        }`
      : undefined,
    createdAt: reservation.createdAt,
    updatedAt: reservation.updatedAt,
  }));

// Convert to slim format
const demoPackageReservationsSlim: PackageReservationSlimDto[] =
  demoPackageReservations.map((reservation) => ({
    id: reservation.id,
    reservationNumber: reservation.reservationNumber,
    referenceNumber: reservation.referenceNumber,
    packageName: reservation.package.name,
    startDate: reservation.startDate,
    endDate: reservation.endDate,
    totalNumberOfGuests: reservation.totalNumberOfGuests,
    status: reservation.status,
    total: reservation.total,
    primaryGuestName: reservation.guests.find((g) => g.isPrimaryGuest)
      ? `${reservation.guests.find((g) => g.isPrimaryGuest)?.guest.firstName} ${
          reservation.guests.find((g) => g.isPrimaryGuest)?.guest.lastName
        }`
      : undefined,
  }));

// Demo API functions
export async function createPackageReservationDemo(
  data: CreatePackageReservationDto
): Promise<PackageReservationIdResponse> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  const newId = `pkg-res-${Date.now()}`;
  return {
    status: ApiStatus.SUCCESS,
    message: "Package reservation created successfully",
    data: { id: newId },
  };
}

export async function bulkCreatePackageReservationsDemo(
  packageReservations: CreatePackageReservationDto[]
): Promise<BulkPackageReservationIdsResponse> {
  await new Promise((resolve) => setTimeout(resolve, 800));

  const ids = packageReservations.map((_, index) => `pkg-res-bulk-${Date.now()}-${index}`);
  return {
    status: ApiStatus.SUCCESS,
    message: `${packageReservations.length} package reservations created successfully`,
    data: { ids },
  };
}

export async function getPackageReservationsTableDataDemo(
  params: GetPackageReservationsSchema
): Promise<PackageReservationPaginatedResponse> {
  await new Promise((resolve) => setTimeout(resolve, 300));

  let filteredData = [...demoPackageReservationsTableData];

  // Apply filters
  if (params.reservationNumber) {
    filteredData = filteredData.filter((item) =>
      item.reservationNumber.toLowerCase().includes(params.reservationNumber!.toLowerCase())
    );
  }

  if (params.status) {
    filteredData = filteredData.filter((item) => item.status === params.status);
  }

  if (params.paymentStatus) {
    filteredData = filteredData.filter((item) => item.paymentStatus === params.paymentStatus);
  }

  // Apply pagination
  const page = params.page || 1;
  const perPage = params.perPage || 10;
  const startIndex = (page - 1) * perPage;
  const endIndex = startIndex + perPage;
  const paginatedData = filteredData.slice(startIndex, endIndex);

  const response: PaginatedPackageReservationsResponseDto = {
    data: paginatedData,
    meta: {
      total: filteredData.length,
      page,
      totalPages: Math.ceil(filteredData.length / perPage),
    },
  };

  return {
    status: ApiStatus.SUCCESS,
    message: "Package reservations retrieved successfully",
    data: response,
  };
}

export async function checkPackageReservationNumberAvailabilityDemo(
  reservationNumber: string
): Promise<PackageReservationNumberAvailabilityResponse> {
  await new Promise((resolve) => setTimeout(resolve, 200));

  const exists = demoPackageReservations.some(
    (reservation) => reservation.reservationNumber === reservationNumber
  );

  return {
    status: ApiStatus.SUCCESS,
    message: "Availability checked successfully",
    data: { available: !exists },
  };
}

export async function checkPackageReferenceNumberAvailabilityDemo(
  referenceNumber: string
): Promise<PackageReservationNumberAvailabilityResponse> {
  await new Promise((resolve) => setTimeout(resolve, 200));

  const exists = demoPackageReservations.some(
    (reservation) => reservation.referenceNumber === referenceNumber
  );

  return {
    status: ApiStatus.SUCCESS,
    message: "Availability checked successfully",
    data: { available: !exists },
  };
}

export async function getPackageReservationsSlimDemo(): Promise<SimplePackageReservationResponse> {
  await new Promise((resolve) => setTimeout(resolve, 200));

  return {
    status: ApiStatus.SUCCESS,
    message: "Package reservations retrieved successfully",
    data: demoPackageReservationsSlim,
  };
}

export async function getPackageReservationDemo(id: string): Promise<PackageReservationResponse> {
  await new Promise((resolve) => setTimeout(resolve, 300));

  const reservation = demoPackageReservations.find((item) => item.id === id);

  if (!reservation) {
    return {
      status: ApiStatus.FAIL,
      message: "Package reservation not found",
      data: null,
    };
  }

  return {
    status: ApiStatus.SUCCESS,
    message: "Package reservation retrieved successfully",
    data: reservation,
  };
}

export async function updatePackageReservationDemo(
  id: string,
  data: UpdatePackageReservationDto
): Promise<PackageReservationIdResponse> {
  await new Promise((resolve) => setTimeout(resolve, 500));

  const exists = demoPackageReservations.some((reservation) => reservation.id === id);

  if (!exists) {
    return {
      status: ApiStatus.FAIL,
      message: "Package reservation not found",
      data: null,
    };
  }

  return {
    status: ApiStatus.SUCCESS,
    message: "Package reservation updated successfully",
    data: { id },
  };
}

export async function deletePackageReservationDemo(
  id: string
): Promise<ApiResponse<{ success: boolean; message: string }>> {
  await new Promise((resolve) => setTimeout(resolve, 400));

  const exists = demoPackageReservations.some((reservation) => reservation.id === id);

  if (!exists) {
    return {
      status: ApiStatus.FAIL,
      message: "Package reservation not found",
      data: null,
    };
  }

  return {
    status: ApiStatus.SUCCESS,
    message: "Package reservation deleted successfully",
    data: { success: true, message: "Package reservation deleted successfully" },
  };
}

export async function bulkDeletePackageReservationsDemo(
  packageReservationIds: string[]
): Promise<BulkDeletePackageReservationResponse> {
  await new Promise((resolve) => setTimeout(resolve, 600));

  return {
    status: ApiStatus.SUCCESS,
    message: `${packageReservationIds.length} package reservations deleted successfully`,
    data: {
      deleted: packageReservationIds.length,
      message: `${packageReservationIds.length} package reservations deleted successfully`,
      deletedIds: packageReservationIds,
    },
  };
}

export async function updatePackageReservationStatusDemo(
  id: string,
  status: PackageReservationStatus
): Promise<PackageReservationIdResponse> {
  await new Promise((resolve) => setTimeout(resolve, 300));

  const exists = demoPackageReservations.some((reservation) => reservation.id === id);

  if (!exists) {
    return {
      status: ApiStatus.FAIL,
      message: "Package reservation not found",
      data: null,
    };
  }

  return {
    status: ApiStatus.SUCCESS,
    message: "Package reservation status updated successfully",
    data: { id },
  };
}

export async function bulkUpdatePackageReservationStatusDemo(
  packageReservationIds: string[],
  status: PackageReservationStatus
): Promise<BulkUpdatePackageReservationStatusResponse> {
  await new Promise((resolve) => setTimeout(resolve, 500));

  return {
    status: ApiStatus.SUCCESS,
    message: `Status updated for ${packageReservationIds.length} package reservations`,
    data: {
      updated: packageReservationIds.length,
      message: `Status updated for ${packageReservationIds.length} package reservations`,
      updatedIds: packageReservationIds,
    },
  };
}
