import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryResult,
} from "@tanstack/react-query";
import {
  PackageReservationPaginatedResponse,
  PackageReservationIdResponse,
  BulkPackageReservationIdsResponse,
  BulkDeletePackageReservationResponse,
  PackageReservationNumberAvailabilityResponse,
  SimplePackageReservationResponse,
  PackageReservationResponse,
  CreatePackageReservationDto,
  UpdatePackageReservationDto,
  PackageReservationStatus,
  BulkUpdatePackageReservationStatusResponse,
} from "@/types/package-reservation";
import { ApiResponse } from "@/types/common";
import {
  GetPackageReservationsSchema,
  CreatePackageReservationSchema,
  UpdatePackageReservationSchema,
  CheckPackageAvailabilitySchema,
  CalculatePackagePricingSchema,
} from "./validations";
import {
  getPackageReservationsTableData,
  getPackageReservationsSlim,
  getPackageReservation,
  checkPackageReservationNumberAvailability,
  checkPackageReferenceNumberAvailability,
  createPackageReservation,
  bulkCreatePackageReservations,
  updatePackageReservation,
  deletePackageReservation,
  bulkDeletePackageReservations,
  updatePackageReservationStatus,
  bulkUpdatePackageReservationStatus,
  checkPackageAvailability,
  calculatePackagePricing,
  confirmPackageReservation,
  cancelPackageReservation,
  sendPackageConfirmation,
  sendPackageReminder,
} from "./queries";

// Query keys for cache management
export const packageReservationKeys = {
  all: ["packageReservations"] as const,
  lists: () => [...packageReservationKeys.all, "list"] as const,
  list: (filters: GetPackageReservationsSchema) =>
    [...packageReservationKeys.lists(), filters] as const,
  details: () => [...packageReservationKeys.all, "detail"] as const,
  detail: (id: string) => [...packageReservationKeys.details(), id] as const,
  slim: () => [...packageReservationKeys.all, "slim"] as const,
  availability: () => [...packageReservationKeys.all, "availability"] as const,
  pricing: () => [...packageReservationKeys.all, "pricing"] as const,
};

// Get package reservations with pagination and filters
export function usePackageReservations(
  params: GetPackageReservationsSchema
): UseQueryResult<PackageReservationPaginatedResponse> {
  return useQuery({
    queryKey: packageReservationKeys.list(params),
    queryFn: () => getPackageReservationsTableData(params),
  });
}

// Get package reservations in slim format (for dropdowns/selects)
export function usePackageReservationsSlim(): UseQueryResult<SimplePackageReservationResponse> {
  return useQuery({
    queryKey: packageReservationKeys.slim(),
    queryFn: getPackageReservationsSlim,
  });
}

// Get a single package reservation by ID
export function usePackageReservation(
  id: string
): UseQueryResult<PackageReservationResponse> {
  return useQuery({
    queryKey: packageReservationKeys.detail(id),
    queryFn: () => getPackageReservation(id),
    enabled: !!id,
  });
}

// Check package reservation number availability
export function useCheckPackageReservationNumberAvailability(
  reservationNumber: string,
  enabled = true
): UseQueryResult<PackageReservationNumberAvailabilityResponse> {
  return useQuery({
    queryKey: ["packageReservationNumberAvailability", reservationNumber],
    queryFn: () => checkPackageReservationNumberAvailability(reservationNumber),
    enabled: enabled && !!reservationNumber,
  });
}

// Check package reference number availability
export function useCheckPackageReferenceNumberAvailability(
  referenceNumber: string,
  enabled = true
): UseQueryResult<PackageReservationNumberAvailabilityResponse> {
  return useQuery({
    queryKey: ["packageReferenceNumberAvailability", referenceNumber],
    queryFn: () => checkPackageReferenceNumberAvailability(referenceNumber),
    enabled: enabled && !!referenceNumber,
  });
}

// Create a new package reservation
export function useCreatePackageReservation() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: CreatePackageReservationSchema) =>
      createPackageReservation(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: packageReservationKeys.lists(),
      });
    },
  });
}

// Bulk create package reservations
export function useBulkCreatePackageReservations() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (packageReservations: CreatePackageReservationDto[]) =>
      bulkCreatePackageReservations(packageReservations),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: packageReservationKeys.lists(),
      });
    },
  });
}

// Update a package reservation
export function useUpdatePackageReservation() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: UpdatePackageReservationSchema;
    }) => updatePackageReservation(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: packageReservationKeys.detail(id),
      });
      queryClient.invalidateQueries({
        queryKey: packageReservationKeys.lists(),
      });
    },
  });
}

// Delete a package reservation
export function useDeletePackageReservation() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string) => deletePackageReservation(id),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: packageReservationKeys.lists(),
      });
    },
  });
}

// Bulk delete package reservations
export function useBulkDeletePackageReservations() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (packageReservationIds: string[]) =>
      bulkDeletePackageReservations(packageReservationIds),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: packageReservationKeys.lists(),
      });
    },
  });
}

// Update package reservation status
export function useUpdatePackageReservationStatus() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({
      id,
      status,
    }: {
      id: string;
      status: PackageReservationStatus;
    }) => updatePackageReservationStatus(id, status),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: packageReservationKeys.detail(id),
      });
      queryClient.invalidateQueries({
        queryKey: packageReservationKeys.lists(),
      });
    },
  });
}

// Bulk update package reservation status
export function useBulkUpdatePackageReservationStatus() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({
      packageReservationIds,
      status,
    }: {
      packageReservationIds: string[];
      status: PackageReservationStatus;
    }) => bulkUpdatePackageReservationStatus(packageReservationIds, status),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: packageReservationKeys.lists(),
      });
    },
  });
}

// Check package availability
export function useCheckPackageAvailability() {
  return useMutation({
    mutationFn: (data: CheckPackageAvailabilitySchema) =>
      checkPackageAvailability(data),
  });
}

// Calculate package pricing
export function useCalculatePackagePricing() {
  return useMutation({
    mutationFn: (data: CalculatePackagePricingSchema) =>
      calculatePackagePricing(data),
  });
}

// Confirm package reservation
export function useConfirmPackageReservation() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data?: any }) =>
      confirmPackageReservation(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: packageReservationKeys.detail(id),
      });
      queryClient.invalidateQueries({
        queryKey: packageReservationKeys.lists(),
      });
    },
  });
}

// Cancel package reservation
export function useCancelPackageReservation() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: { cancellationReason?: string };
    }) => cancelPackageReservation(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: packageReservationKeys.detail(id),
      });
      queryClient.invalidateQueries({
        queryKey: packageReservationKeys.lists(),
      });
    },
  });
}

// Send package confirmation
export function useSendPackageConfirmation() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data?: any }) =>
      sendPackageConfirmation(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: packageReservationKeys.detail(id),
      });
    },
  });
}

// Send package reminder
export function useSendPackageReminder() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data?: any }) =>
      sendPackageReminder(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: packageReservationKeys.detail(id),
      });
    },
  });
}
