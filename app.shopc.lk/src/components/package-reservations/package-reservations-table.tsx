"use client";

import * as React from "react";
import type {
  DataTableAdvancedFilterField,
  DataTableFilterField,
  DataTableRowAction,
} from "@/types";
import { BaseTable } from "@/components/shared/base-table";
import { getPackageReservationsTableData } from "@/lib/package-reservations/queries";
import { DeletePackageReservationsDialog } from "./delete-package-reservations-dialog";
import { PackageReservationsTableToolbarActions } from "./package-reservations-table-toolbar-actions";
import { PackageReservationsTableFloatingBar } from "./package-reservations-table-floating-bar";
import { PackageReservationSheet } from "./package-reservation-sheet";
import { PackageReservationDetails } from "./package-reservation-details";
import { PackageReservationDetailsContent } from "./package-reservation-details-content";
import { useSearchParams } from "next/navigation";
import { getColumns } from "./package-reservations-table-columns";
import {
  PackageReservationTableData,
  PackageReservationStatus,
  PaymentStatus,
} from "@/types/package-reservation";
import { usePackageReservations } from "@/lib/package-reservations/hooks";
import { useQueryClient } from "@tanstack/react-query";
import { packageReservationKeys } from "@/lib/package-reservations/hooks";
import { ApiStatus } from "@/types/common";

interface PackageReservationsTableProps {
  isDemo?: boolean;
}

export function PackageReservationsTable({
  isDemo = false,
}: PackageReservationsTableProps) {
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<PackageReservationTableData> | null>(
      null
    );
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  // Parse search params
  const searchParamsValues = React.useMemo(() => {
    const page = Number(searchParams.get("page")) || 1;
    const perPage = Number(searchParams.get("perPage")) || 10;
    const sort = searchParams.get("sort");
    const status = searchParams.get("status");
    const paymentStatus = searchParams.get("paymentStatus");
    const packageId = searchParams.get("packageId");
    const reservationNumber = searchParams.get("reservationNumber");
    const referenceNumber = searchParams.get("referenceNumber");
    const from = searchParams.get("from");
    const to = searchParams.get("to");

    return {
      page,
      perPage,
      sort: sort ? JSON.parse(sort) : undefined,
      status: status || undefined,
      paymentStatus: paymentStatus || undefined,
      packageId: packageId || undefined,
      reservationNumber: reservationNumber || undefined,
      referenceNumber: referenceNumber || undefined,
      from: from || undefined,
      to: to || undefined,
    };
  }, [searchParams]);

  // Fetch data
  const {
    data: packageReservationsData,
    isLoading,
    error,
    refetch,
  } = usePackageReservations(searchParamsValues);

  const isHeaderLoading = isLoading && !packageReservationsData;

  // Check if there are active filters
  const hasActiveFilters = React.useMemo(() => {
    return !!(
      searchParamsValues.status ||
      searchParamsValues.paymentStatus ||
      searchParamsValues.packageId ||
      searchParamsValues.startDate ||
      searchParamsValues.endDate ||
      searchParamsValues.search
    );
  }, [searchParamsValues]);

  // Handle refresh
  const handleRefresh = React.useCallback(async () => {
    await refetch();
  }, [refetch]);

  // Handle row actions
  const handleRowAction = React.useCallback(
    (action: DataTableRowAction<PackageReservationTableData>) => {
      setRowAction(action);
    },
    []
  );

  // Define filter fields
  const filterFields: DataTableFilterField<PackageReservationTableData>[] =
    React.useMemo(
      () => [
        {
          id: "reservationNumber",
          label: "Reservation Number",
          type: "text",
        },
        {
          id: "packageName",
          label: "Package",
          type: "text",
        },
        {
          id: "primaryGuestName",
          label: "Guest Name",
          type: "text",
        },
        {
          id: "status",
          label: "Status",
          type: "select",
          options: Object.values(PackageReservationStatus).map((status) => ({
            label: status
              .replace(/_/g, " ")
              .toLowerCase()
              .replace(/\b\w/g, (l) => l.toUpperCase()),
            value: status,
          })),
        },
        {
          id: "paymentStatus",
          label: "Payment Status",
          type: "select",
          options: Object.values(PaymentStatus).map((status) => ({
            label: status
              .replace(/_/g, " ")
              .toLowerCase()
              .replace(/\b\w/g, (l) => l.toUpperCase()),
            value: status,
          })),
        },
      ],
      []
    );

  // Define sort fields
  const sortFields: DataTableAdvancedFilterField<PackageReservationTableData>[] =
    React.useMemo(
      () => [
        {
          id: "reservationNumber",
          label: "Reservation Number",
          type: "text",
        },
        {
          id: "packageName",
          label: "Package",
          type: "text",
        },
        {
          id: "primaryGuestName",
          label: "Guest Name",
          type: "text",
        },
        {
          id: "startDate",
          label: "Check-in Date",
          type: "text",
        },
        {
          id: "endDate",
          label: "Check-out Date",
          type: "text",
        },
        {
          id: "totalNumberOfGuests",
          label: "Total Guests",
          type: "number",
        },
        {
          id: "total",
          label: "Total Amount",
          type: "number",
        },
        {
          id: "createdAt",
          label: "Created Date",
          type: "text",
        },
      ],
      []
    );

  // Actions disabled state
  const isActionsDisabled = isLoading || !!error;

  // Render package reservation details
  const renderPackageReservationDetails = React.useCallback(
    (packageReservation: PackageReservationTableData) => (
      <PackageReservationDetailsContent
        packageReservation={packageReservation}
        isDemo={isDemo}
      />
    ),
    [isDemo]
  );

  return (
    <>
      <BaseTable<
        PackageReservationTableData,
        Awaited<ReturnType<typeof getPackageReservationsTableData>>
      >
        data={packageReservationsData || undefined}
        isLoading={isLoading}
        isHeaderLoading={isHeaderLoading}
        isDemo={isDemo}
        hasActiveFilters={hasActiveFilters}
        currentPage={searchParamsValues.page}
        itemsPerPage={searchParamsValues.perPage}
        columns={getColumns({
          setRowAction,
          isDemo,
          isActionsDisabled,
          onStatusUpdate: (packageReservationId?: string) => {
            queryClient.invalidateQueries({
              queryKey: packageReservationKeys.lists(),
            });
          },
        })}
        filterFields={filterFields}
        sortFields={sortFields}
        FloatingBar={PackageReservationsTableFloatingBar}
        ToolbarActions={PackageReservationsTableToolbarActions}
        onRowAction={handleRowAction}
        defaultSortingId={undefined}
        defaultSortingDesc={false}
        onRefresh={handleRefresh}
        renderDialogContent={renderPackageReservationDetails}
      />
      <PackageReservationDetails
        open={rowAction?.type === "view"}
        onOpenChange={() => setRowAction(null)}
        packageReservation={rowAction?.row.original ?? null}
        isDemo={isDemo}
      />
      <PackageReservationSheet
        open={rowAction?.type === "update"}
        onOpenChange={() => setRowAction(null)}
        packageReservation={rowAction?.row.original ?? null}
        isDemo={isDemo}
        onSuccess={() => {
          queryClient.invalidateQueries({
            queryKey: packageReservationKeys.lists(),
          });
          setRowAction(null);
        }}
      />
      <DeletePackageReservationsDialog
        open={rowAction?.type === "delete"}
        onOpenChange={() => setRowAction(null)}
        packageReservations={
          rowAction?.row.original ? [rowAction.row.original] : []
        }
        showTrigger={false}
        onSuccess={() => {
          queryClient.invalidateQueries({
            queryKey: packageReservationKeys.lists(),
          });
          setRowAction(null);
        }}
        isDemo={isDemo}
      />
    </>
  );
}
