"use client";

import * as React from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Calendar,
  Users,
  DollarSign,
  FileText,
  Package,
  Hash,
  Clock,
} from "lucide-react";
import {
  PackageReservationTableData,
  PackageReservationStatus,
  PaymentStatus,
} from "@/types/package-reservation";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { PackageReservationStatusBadge } from "./package-reservation-status-badge";
import { PaymentStatusBadge } from "./payment-status-badge";

interface PackageReservationDetailsContentProps {
  packageReservation: PackageReservationTableData;
  isDemo?: boolean;
}

export function PackageReservationDetailsContent({
  packageReservation,
  isDemo = false,
}: PackageReservationDetailsContentProps) {
  return (
    <div className="space-y-4 p-4">
      {/* Header */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">{packageReservation.reservationNumber}</h3>
          <div className="flex items-center gap-2">
            <PackageReservationStatusBadge status={packageReservation.status} />
            <PaymentStatusBadge status={packageReservation.paymentStatus} />
          </div>
        </div>
        {packageReservation.referenceNumber && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Hash className="h-4 w-4" />
            <span>Reference: {packageReservation.referenceNumber}</span>
          </div>
        )}
      </div>

      <Separator />

      {/* Package Information */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Package className="h-4 w-4 text-muted-foreground" />
          <h4 className="font-medium">Package Details</h4>
        </div>
        <div className="grid grid-cols-1 gap-3 text-sm">
          <div>
            <p className="text-muted-foreground">Package</p>
            <p className="font-medium">{packageReservation.packageName}</p>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-muted-foreground">Check-in</p>
              <div className="flex items-center gap-2">
                <Calendar className="h-3 w-3 text-muted-foreground" />
                <p className="font-medium">
                  {format(new Date(packageReservation.startDate), "MMM dd, yyyy")}
                </p>
              </div>
            </div>
            <div>
              <p className="text-muted-foreground">Check-out</p>
              <div className="flex items-center gap-2">
                <Calendar className="h-3 w-3 text-muted-foreground" />
                <p className="font-medium">
                  {format(new Date(packageReservation.endDate), "MMM dd, yyyy")}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Separator />

      {/* Guest Information */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-muted-foreground" />
          <h4 className="font-medium">Guest Information</h4>
        </div>
        <div className="grid grid-cols-1 gap-3 text-sm">
          <div>
            <p className="text-muted-foreground">Primary Guest</p>
            <p className="font-medium">{packageReservation.primaryGuestName || "N/A"}</p>
          </div>
          <div className="grid grid-cols-3 gap-4">
            <div>
              <p className="text-muted-foreground">Total</p>
              <p className="font-medium">{packageReservation.totalNumberOfGuests}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Adults</p>
              <p className="font-medium">{packageReservation.numberOfAdults}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Children</p>
              <p className="font-medium">{packageReservation.numberOfChildren}</p>
            </div>
          </div>
        </div>
      </div>

      <Separator />

      {/* Pricing Information */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <DollarSign className="h-4 w-4 text-muted-foreground" />
          <h4 className="font-medium">Pricing</h4>
        </div>
        <div className="text-sm">
          <div className="flex items-center justify-between">
            <p className="text-muted-foreground">Total Amount</p>
            <p className="font-semibold text-lg">${packageReservation.total}</p>
          </div>
        </div>
      </div>

      <Separator />

      {/* Timeline */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Clock className="h-4 w-4 text-muted-foreground" />
          <h4 className="font-medium">Timeline</h4>
        </div>
        <div className="grid grid-cols-1 gap-3 text-sm">
          <div className="flex items-center justify-between">
            <p className="text-muted-foreground">Created</p>
            <p className="font-medium">
              {format(new Date(packageReservation.createdAt), "MMM dd, yyyy")}
            </p>
          </div>
          <div className="flex items-center justify-between">
            <p className="text-muted-foreground">Last Updated</p>
            <p className="font-medium">
              {format(new Date(packageReservation.updatedAt), "MMM dd, yyyy")}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
