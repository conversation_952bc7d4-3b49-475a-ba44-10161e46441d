"use client";

import * as React from "react";
import { type Table } from "@tanstack/react-table";
import { PackageReservationTableData, PackageReservationStatus } from "@/types/package-reservation";
import { BaseTableFloatingBar } from "@/components/shared/base-table-floating-bar";
import { DeletePackageReservationsDialog } from "./delete-package-reservations-dialog";

interface PackageReservationsTableFloatingBarProps {
  table: Table<PackageReservationTableData>;
  onRefresh?: () => Promise<void>;
  isDemo?: boolean;
}

export function PackageReservationsTableFloatingBar({
  table,
  onRefresh,
  isDemo = false,
}: PackageReservationsTableFloatingBarProps) {
  const [showDeleteDialog, setShowDeleteDialog] = React.useState(false);

  const selectedRows = table.getFilteredSelectedRowModel().rows;
  const selectedPackageReservations = selectedRows.map((row) => row.original);

  const statusUpdateOptions = [
    { value: PackageReservationStatus.INQUIRY, label: "Inquiry" },
    { value: PackageReservationStatus.PENDING, label: "Pending" },
    { value: PackageReservationStatus.CONFIRMED, label: "Confirmed" },
    { value: PackageReservationStatus.PARTIALLY_PAID, label: "Partially Paid" },
    { value: PackageReservationStatus.PAID, label: "Paid" },
    { value: PackageReservationStatus.IN_PROGRESS, label: "In Progress" },
    { value: PackageReservationStatus.COMPLETED, label: "Completed" },
    { value: PackageReservationStatus.CANCELLED, label: "Cancelled" },
    { value: PackageReservationStatus.NO_SHOW, label: "No Show" },
    { value: PackageReservationStatus.REFUNDED, label: "Refunded" },
  ];

  return (
    <>
      <BaseTableFloatingBar<PackageReservationTableData>
        table={table}
        onRefresh={onRefresh}
        isDemo={isDemo}
        title="package reservations"
        onDelete={() => setShowDeleteDialog(true)}
        statusUpdateOptions={statusUpdateOptions}
        statusField="status"
      />
      <DeletePackageReservationsDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        packageReservations={selectedPackageReservations}
        showTrigger={false}
        onSuccess={() => {
          table.resetRowSelection();
          onRefresh?.();
        }}
        isDemo={isDemo}
      />
    </>
  );
}
