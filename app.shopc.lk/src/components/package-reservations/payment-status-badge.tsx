"use client";

import { Badge } from "@/components/ui/badge";
import { PaymentStatus } from "@/types/package-reservation";
import { cn } from "@/lib/utils";

interface PaymentStatusBadgeProps {
  status: PaymentStatus;
  className?: string;
}

export function PaymentStatusBadge({
  status,
  className,
}: PaymentStatusBadgeProps) {
  const getStatusColor = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.PENDING:
        return "bg-yellow-100 text-yellow-800 hover:bg-yellow-100";
      case PaymentStatus.PARTIAL:
        return "bg-orange-100 text-orange-800 hover:bg-orange-100";
      case PaymentStatus.PAID:
        return "bg-green-100 text-green-800 hover:bg-green-100";
      case PaymentStatus.REFUNDED:
        return "bg-blue-100 text-blue-800 hover:bg-blue-100";
      case PaymentStatus.CANCELLED:
        return "bg-red-100 text-red-800 hover:bg-red-100";
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-100";
    }
  };

  const getStatusLabel = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.PENDING:
        return "Pending";
      case PaymentStatus.PARTIAL:
        return "Partial";
      case PaymentStatus.PAID:
        return "Paid";
      case PaymentStatus.REFUNDED:
        return "Refunded";
      case PaymentStatus.CANCELLED:
        return "Cancelled";
      default:
        return status;
    }
  };

  return (
    <Badge
      className={cn(
        "font-medium",
        getStatusColor(status),
        className
      )}
    >
      {getStatusLabel(status)}
    </Badge>
  );
}
