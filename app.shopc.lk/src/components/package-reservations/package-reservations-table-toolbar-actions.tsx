"use client";

import { type Table } from "@tanstack/react-table";
import { PackageReservationSheet } from "./package-reservation-sheet";
import { BaseTableToolbarActions } from "@/components/shared/base-table-toolbar-actions";
import { useState } from "react";
import { PackageReservationTableData } from "@/types/package-reservation";

interface PackageReservationsTableToolbarActionsProps {
  table: Table<PackageReservationTableData>;
  onRefresh?: () => Promise<void>;
  rowSelection: boolean;
  onRowSelectionChange: (value: boolean) => void;
  isDemo?: boolean;
}

export function PackageReservationsTableToolbarActions({
  table,
  onRefresh,
  rowSelection,
  onRowSelectionChange,
  isDemo = false,
}: PackageReservationsTableToolbarActionsProps) {
  const [open, setOpen] = useState(false);

  return (
    <>
      <BaseTableToolbarActions<PackageReservationTableData>
        table={table}
        onRefresh={onRefresh}
        rowSelection={rowSelection}
        onRowSelectionChange={onRowSelectionChange}
        isDemo={isDemo}
        title="Package Reservations"
        addButton={
          <PackageReservationSheet
            onSuccess={() => onRefresh?.()}
            isDemo={isDemo}
            packageReservation={null}
            open={open}
            onOpenChange={setOpen}
          />
        }
      />
    </>
  );
}
