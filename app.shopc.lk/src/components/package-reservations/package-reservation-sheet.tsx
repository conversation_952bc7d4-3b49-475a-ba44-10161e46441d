"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import {
  Info,
  Calendar,
  Users,
  DollarSign,
  FileText,
  Check,
  X,
  Loader2,
} from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { packageReservationFormSchema } from "@/lib/package-reservations/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  PackageReservationTableData,
  PackageReservationStatus,
  PaymentStatus,
  ReservationSource,
  DiscountType,
  TaxType,
  UpdatePackageReservationDto,
} from "@/types/package-reservation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  usePackageReservationData,
  usePackageReservationNumberAvailability,
  usePackageReservationReferenceNumberAvailability,
  useCreatePackageReservation,
  useUpdatePackageReservation,
} from "@/lib/package-reservations/hooks";
import { usePackagesSlim } from "@/lib/packages/hooks";
import { useEffect } from "react";
import { format } from "date-fns";

interface PackageReservationSheetProps {
  packageReservation: PackageReservationTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (packageReservation?: PackageReservationTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof packageReservationFormSchema>;

export function PackageReservationSheet({
  packageReservation,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: PackageReservationSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Debounced values for availability checking
  const [debouncedReservationNumber, setDebouncedReservationNumber] =
    React.useState<string>("");
  const [debouncedReferenceNumber, setDebouncedReferenceNumber] =
    React.useState<string>("");

  // Fetch complete package reservation data if updating
  const {
    data: fullPackageReservationResponse,
    isLoading: isLoadingPackageReservation,
  } = usePackageReservationData(packageReservation?.id || "", isDemo);
  const fullPackageReservation = fullPackageReservationResponse?.data;

  // Fetch packages for selection
  const { data: packagesResponse } = usePackagesSlim(isDemo);
  const packages = React.useMemo(
    () => packagesResponse?.data || [],
    [packagesResponse]
  );

  // Mutations
  const createPackageReservationMutation = useCreatePackageReservation(isDemo);
  const updatePackageReservationMutation = useUpdatePackageReservation(isDemo);

  // Availability checks
  const shouldCheckReservationNumberAvailability = React.useMemo(() => {
    if (!debouncedReservationNumber || debouncedReservationNumber.length < 2)
      return null;
    if (
      packageReservation &&
      fullPackageReservation?.reservationNumber === debouncedReservationNumber
    )
      return null;
    return true;
  }, [debouncedReservationNumber, packageReservation, fullPackageReservation]);

  const shouldCheckReferenceNumberAvailability = React.useMemo(() => {
    if (!debouncedReferenceNumber || debouncedReferenceNumber.length < 2)
      return null;
    if (
      packageReservation &&
      fullPackageReservation?.referenceNumber === debouncedReferenceNumber
    )
      return null;
    return true;
  }, [debouncedReferenceNumber, packageReservation, fullPackageReservation]);

  const {
    data: reservationNumberAvailability,
    isLoading: isCheckingReservationNumber,
  } = usePackageReservationNumberAvailability(
    debouncedReservationNumber,
    shouldCheckReservationNumberAvailability,
    isDemo
  );

  const {
    data: referenceNumberAvailability,
    isLoading: isCheckingReferenceNumber,
  } = usePackageReservationReferenceNumberAvailability(
    debouncedReferenceNumber,
    shouldCheckReferenceNumberAvailability,
    isDemo
  );

  // Form setup
  const form = useForm<FormData>({
    resolver: zodResolver(packageReservationFormSchema),
    defaultValues: {
      reservationNumber: "",
      referenceNumber: "",
      packageId: "",
      startDate: "",
      endDate: "",
      numberOfAdults: 1,
      numberOfChildren: 0,
      status: PackageReservationStatus.INQUIRY,
      reservationSource: ReservationSource.ONLINE,
      paymentStatus: PaymentStatus.PENDING,
      subtotal: "",
      total: "",
      depositPaid: "",
      balanceDue: "",
      discountType: DiscountType.PERCENTAGE,
      discountValue: "",
      discountAmount: "",
      taxType: TaxType.INCLUSIVE,
      taxAmount: "",
      notes: "",
      cancellationReason: "",
      cancellationDate: "",
      confirmationSent: false,
      confirmationSentAt: "",
      reminderSent: false,
      reminderSentAt: "",
      guests: [],
    },
  });

  const {
    formState: { errors },
    reset,
    handleSubmit,
    watch,
  } = form;

  // Watch form values for calculations
  const watchedValues = watch([
    "numberOfAdults",
    "numberOfChildren",
    "subtotal",
    "discountType",
    "discountValue",
    "taxType",
    "taxAmount",
  ]);

  // Calculate total number of guests
  React.useEffect(() => {
    const numberOfAdults = form.getValues("numberOfAdults") || 0;
    const numberOfChildren = form.getValues("numberOfChildren") || 0;
    const totalGuests = numberOfAdults + numberOfChildren;

    // Update total guests if it's different
    if (totalGuests !== form.getValues("totalNumberOfGuests")) {
      form.setValue("totalNumberOfGuests", totalGuests, {
        shouldValidate: true,
      });
    }
  }, [form, watchedValues[0], watchedValues[1]]);

  // Reset form when package reservation changes or dialog opens/closes
  React.useEffect(() => {
    if (open) {
      if (packageReservation && fullPackageReservation) {
        // Update mode - populate form with existing data
        reset({
          reservationNumber: fullPackageReservation.reservationNumber,
          referenceNumber: fullPackageReservation.referenceNumber || "",
          packageId: fullPackageReservation.packageId,
          startDate: format(
            new Date(fullPackageReservation.startDate),
            "yyyy-MM-dd"
          ),
          endDate: format(
            new Date(fullPackageReservation.endDate),
            "yyyy-MM-dd"
          ),
          totalNumberOfGuests: fullPackageReservation.totalNumberOfGuests,
          numberOfAdults: fullPackageReservation.numberOfAdults,
          numberOfChildren: fullPackageReservation.numberOfChildren,
          status: fullPackageReservation.status,
          reservationSource: fullPackageReservation.reservationSource,
          paymentStatus: fullPackageReservation.paymentStatus,
          subtotal: fullPackageReservation.subtotal,
          total: fullPackageReservation.total,
          depositPaid: fullPackageReservation.depositPaid || "",
          balanceDue: fullPackageReservation.balanceDue || "",
          discountType: fullPackageReservation.discountType,
          discountValue: fullPackageReservation.discountValue || "",
          discountAmount: fullPackageReservation.discountAmount || "",
          taxType: fullPackageReservation.taxType,
          taxAmount: fullPackageReservation.taxAmount || "",
          notes: fullPackageReservation.notes || "",
          cancellationReason: fullPackageReservation.cancellationReason || "",
          cancellationDate: fullPackageReservation.cancellationDate
            ? format(
                new Date(fullPackageReservation.cancellationDate),
                "yyyy-MM-dd"
              )
            : "",
          confirmationSent: fullPackageReservation.confirmationSent,
          confirmationSentAt: fullPackageReservation.confirmationSentAt
            ? format(
                new Date(fullPackageReservation.confirmationSentAt),
                "yyyy-MM-dd'T'HH:mm"
              )
            : "",
          reminderSent: fullPackageReservation.reminderSent,
          reminderSentAt: fullPackageReservation.reminderSentAt
            ? format(
                new Date(fullPackageReservation.reminderSentAt),
                "yyyy-MM-dd'T'HH:mm"
              )
            : "",
          guests: fullPackageReservation.guests || [],
        });
      } else {
        // Create mode - reset to defaults
        reset({
          reservationNumber: "",
          referenceNumber: "",
          packageId: "",
          startDate: "",
          endDate: "",
          numberOfAdults: 1,
          numberOfChildren: 0,
          status: PackageReservationStatus.INQUIRY,
          reservationSource: ReservationSource.ONLINE,
          paymentStatus: PaymentStatus.PENDING,
          subtotal: "",
          total: "",
          depositPaid: "",
          balanceDue: "",
          discountType: DiscountType.PERCENTAGE,
          discountValue: "",
          discountAmount: "",
          taxType: TaxType.INCLUSIVE,
          taxAmount: "",
          notes: "",
          cancellationReason: "",
          cancellationDate: "",
          confirmationSent: false,
          confirmationSentAt: "",
          reminderSent: false,
          reminderSentAt: "",
          guests: [],
        });
      }
    }
  }, [open, packageReservation, fullPackageReservation, reset]);

  // Generate reservation number
  const generateReservationNumber = () => {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substring(2, 5).toUpperCase();
    return `PKG-${timestamp}-${random}`;
  };

  // Auto-generate reservation number when creating new reservation
  React.useEffect(() => {
    if (open && !packageReservation && !form.getValues("reservationNumber")) {
      form.setValue("reservationNumber", generateReservationNumber());
    }
  }, [open, packageReservation, form]);

  // Handle form submission
  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    try {
      if (packageReservation) {
        // Update existing package reservation
        const updateData: UpdatePackageReservationDto = {
          reservationNumber: data.reservationNumber,
          referenceNumber: data.referenceNumber || undefined,
          packageId: data.packageId,
          startDate: data.startDate,
          endDate: data.endDate,
          totalNumberOfGuests: data.totalNumberOfGuests,
          numberOfAdults: data.numberOfAdults,
          numberOfChildren: data.numberOfChildren,
          status: data.status,
          reservationSource: data.reservationSource,
          paymentStatus: data.paymentStatus,
          subtotal: data.subtotal || undefined,
          total: data.total || undefined,
          depositPaid: data.depositPaid || undefined,
          balanceDue: data.balanceDue || undefined,
          discountType: data.discountType,
          discountValue: data.discountValue || undefined,
          discountAmount: data.discountAmount || undefined,
          taxType: data.taxType,
          taxAmount: data.taxAmount || undefined,
          notes: data.notes || undefined,
          cancellationReason: data.cancellationReason || undefined,
          cancellationDate: data.cancellationDate || undefined,
          confirmationSent: data.confirmationSent,
          confirmationSentAt: data.confirmationSentAt || undefined,
          reminderSent: data.reminderSent,
          reminderSentAt: data.reminderSentAt || undefined,
          guests: data.guests,
        };

        const response = await updatePackageReservationMutation.mutateAsync({
          id: packageReservation.id,
          data: updateData,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Package reservation updated successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as PackageReservationTableData);
          return;
        }
        toast.error(response.message || "Failed to update package reservation");
        return;
      } else {
        // Create new package reservation
        const createData = {
          reservationNumber: data.reservationNumber,
          referenceNumber: data.referenceNumber || undefined,
          packageId: data.packageId,
          startDate: data.startDate,
          endDate: data.endDate,
          totalNumberOfGuests: data.totalNumberOfGuests,
          numberOfAdults: data.numberOfAdults,
          numberOfChildren: data.numberOfChildren,
          status: data.status,
          reservationSource: data.reservationSource,
          paymentStatus: data.paymentStatus,
          subtotal: data.subtotal || undefined,
          total: data.total || undefined,
          depositPaid: data.depositPaid || undefined,
          balanceDue: data.balanceDue || undefined,
          discountType: data.discountType,
          discountValue: data.discountValue || undefined,
          discountAmount: data.discountAmount || undefined,
          taxType: data.taxType,
          taxAmount: data.taxAmount || undefined,
          notes: data.notes || undefined,
          cancellationReason: data.cancellationReason || undefined,
          cancellationDate: data.cancellationDate || undefined,
          confirmationSent: data.confirmationSent,
          confirmationSentAt: data.confirmationSentAt || undefined,
          reminderSent: data.reminderSent,
          reminderSentAt: data.reminderSentAt || undefined,
          guests: data.guests,
        };

        const response = await createPackageReservationMutation.mutateAsync({
          data: createData,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Package reservation created successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as PackageReservationTableData);
          return;
        }
        toast.error(response.message || "Failed to create package reservation");
        return;
      }
    } catch (error) {
      console.error("Failed to save package reservation:", error);
      toast.error("Failed to save package reservation");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Debounce form values for availability checking
  React.useEffect(() => {
    const subscription = form.watch((value) => {
      const timer = setTimeout(() => {
        if (value.reservationNumber !== undefined) {
          setDebouncedReservationNumber(value.reservationNumber || "");
        }
        if (value.referenceNumber !== undefined) {
          setDebouncedReferenceNumber(value.referenceNumber || "");
        }
      }, 500); // 500ms debounce

      return () => clearTimeout(timer);
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Helper component for availability indicators
  const AvailabilityIndicator = ({
    isLoading,
    isAvailable,
    shouldCheck,
    fieldName,
  }: {
    isLoading: boolean;
    isAvailable: boolean | undefined | null;
    shouldCheck: boolean | null;
    fieldName: string;
  }) => {
    if (!shouldCheck) return null;

    if (isLoading) {
      return (
        <div className="flex items-center gap-1 text-muted-foreground">
          <Loader2 className="h-3 w-3 animate-spin" />
          <span className="text-xs">Checking...</span>
        </div>
      );
    }

    if (isAvailable === true) {
      return (
        <div className="flex items-center gap-1 text-green-600">
          <Check className="h-3 w-3" />
          <span className="text-xs">{fieldName} is available</span>
        </div>
      );
    }

    if (isAvailable === false) {
      return (
        <div className="flex items-center gap-1 text-red-600">
          <X className="h-3 w-3" />
          <span className="text-xs">{fieldName} is already taken</span>
        </div>
      );
    }

    return null;
  };

  // Check for form errors in each section
  const hasBasicInfoErrors = !!(
    errors.reservationNumber ||
    errors.referenceNumber ||
    errors.packageId ||
    errors.startDate ||
    errors.endDate ||
    errors.numberOfAdults ||
    errors.numberOfChildren
  );

  const hasStatusErrors = !!(
    errors.status ||
    errors.reservationSource ||
    errors.paymentStatus
  );

  const hasPricingErrors = !!(
    errors.subtotal ||
    errors.total ||
    errors.depositPaid ||
    errors.balanceDue ||
    errors.discountType ||
    errors.discountValue ||
    errors.discountAmount ||
    errors.taxType ||
    errors.taxAmount
  );

  const hasNotesErrors = !!(
    errors.notes ||
    errors.cancellationReason ||
    errors.cancellationDate
  );

  const hasConfirmationErrors = !!(
    errors.confirmationSent ||
    errors.confirmationSentAt ||
    errors.reminderSent ||
    errors.reminderSentAt
  );

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Reservation Number</Label>
                <Input
                  {...form.register("reservationNumber")}
                  name="reservationNumber"
                  placeholder="Enter reservation number"
                  className={cn(
                    errors.reservationNumber && "border-destructive",
                    shouldCheckReservationNumberAvailability &&
                      reservationNumberAvailability?.data?.isAvailable ===
                        false &&
                      "border-destructive"
                  )}
                />
                {errors.reservationNumber && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.reservationNumber.message}
                  </p>
                )}
                <AvailabilityIndicator
                  isLoading={isCheckingReservationNumber}
                  isAvailable={reservationNumberAvailability?.data?.isAvailable}
                  shouldCheck={shouldCheckReservationNumberAvailability}
                  fieldName="Reservation number"
                />
              </div>

              <div>
                <Label>Reference Number (Optional)</Label>
                <Input
                  {...form.register("referenceNumber")}
                  name="referenceNumber"
                  placeholder="Enter reference number"
                  className={cn(
                    errors.referenceNumber && "border-destructive",
                    shouldCheckReferenceNumberAvailability &&
                      referenceNumberAvailability?.data?.isAvailable ===
                        false &&
                      "border-destructive"
                  )}
                />
                {errors.referenceNumber && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.referenceNumber.message}
                  </p>
                )}
                <AvailabilityIndicator
                  isLoading={isCheckingReferenceNumber}
                  isAvailable={referenceNumberAvailability?.data?.isAvailable}
                  shouldCheck={shouldCheckReferenceNumberAvailability}
                  fieldName="Reference number"
                />
              </div>

              <div>
                <Label>Package</Label>
                <Select
                  value={form.watch("packageId")}
                  onValueChange={(value) =>
                    form.setValue("packageId", value, { shouldValidate: true })
                  }
                >
                  <SelectTrigger
                    className={cn(errors.packageId && "border-destructive")}
                  >
                    <SelectValue placeholder="Select a package" />
                  </SelectTrigger>
                  <SelectContent>
                    {packages.map((pkg) => (
                      <SelectItem key={pkg.id} value={pkg.id}>
                        {pkg.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.packageId && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.packageId.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Check-in Date</Label>
                  <Input
                    {...form.register("startDate")}
                    name="startDate"
                    type="date"
                    className={cn(errors.startDate && "border-destructive")}
                  />
                  {errors.startDate && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.startDate.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Check-out Date</Label>
                  <Input
                    {...form.register("endDate")}
                    name="endDate"
                    type="date"
                    className={cn(errors.endDate && "border-destructive")}
                  />
                  {errors.endDate && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.endDate.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Number of Adults</Label>
                  <Input
                    {...form.register("numberOfAdults", {
                      valueAsNumber: true,
                    })}
                    name="numberOfAdults"
                    type="number"
                    min="1"
                    placeholder="Enter number of adults"
                    className={cn(
                      errors.numberOfAdults && "border-destructive"
                    )}
                  />
                  {errors.numberOfAdults && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.numberOfAdults.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Number of Children</Label>
                  <Input
                    {...form.register("numberOfChildren", {
                      valueAsNumber: true,
                    })}
                    name="numberOfChildren"
                    type="number"
                    min="0"
                    placeholder="Enter number of children"
                    className={cn(
                      errors.numberOfChildren && "border-destructive"
                    )}
                  />
                  {errors.numberOfChildren && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.numberOfChildren.message}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "status-info",
      title: "Status Information",
      icon: <Calendar className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasStatusErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Status Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Reservation Status</Label>
                <Select
                  value={form.watch("status")}
                  onValueChange={(value) =>
                    form.setValue("status", value as PackageReservationStatus, {
                      shouldValidate: true,
                    })
                  }
                >
                  <SelectTrigger
                    className={cn(errors.status && "border-destructive")}
                  >
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(PackageReservationStatus).map((status) => (
                      <SelectItem key={status} value={status}>
                        {status
                          .replace(/_/g, " ")
                          .toLowerCase()
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.status.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Reservation Source</Label>
                <Select
                  value={form.watch("reservationSource")}
                  onValueChange={(value) =>
                    form.setValue(
                      "reservationSource",
                      value as ReservationSource,
                      { shouldValidate: true }
                    )
                  }
                >
                  <SelectTrigger
                    className={cn(
                      errors.reservationSource && "border-destructive"
                    )}
                  >
                    <SelectValue placeholder="Select source" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(ReservationSource).map((source) => (
                      <SelectItem key={source} value={source}>
                        {source
                          .replace(/_/g, " ")
                          .toLowerCase()
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.reservationSource && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.reservationSource.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Payment Status</Label>
                <Select
                  value={form.watch("paymentStatus")}
                  onValueChange={(value) =>
                    form.setValue("paymentStatus", value as PaymentStatus, {
                      shouldValidate: true,
                    })
                  }
                >
                  <SelectTrigger
                    className={cn(errors.paymentStatus && "border-destructive")}
                  >
                    <SelectValue placeholder="Select payment status" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(PaymentStatus).map((status) => (
                      <SelectItem key={status} value={status}>
                        {status
                          .replace(/_/g, " ")
                          .toLowerCase()
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.paymentStatus && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.paymentStatus.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "pricing-info",
      title: "Pricing Information",
      icon: <DollarSign className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasPricingErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Pricing Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Subtotal</Label>
                  <Input
                    {...form.register("subtotal")}
                    name="subtotal"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className={cn(errors.subtotal && "border-destructive")}
                  />
                  {errors.subtotal && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.subtotal.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Total</Label>
                  <Input
                    {...form.register("total")}
                    name="total"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className={cn(errors.total && "border-destructive")}
                  />
                  {errors.total && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.total.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Deposit Paid</Label>
                  <Input
                    {...form.register("depositPaid")}
                    name="depositPaid"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className={cn(errors.depositPaid && "border-destructive")}
                  />
                  {errors.depositPaid && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.depositPaid.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Balance Due</Label>
                  <Input
                    {...form.register("balanceDue")}
                    name="balanceDue"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className={cn(errors.balanceDue && "border-destructive")}
                  />
                  {errors.balanceDue && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.balanceDue.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label>Discount Type</Label>
                  <Select
                    value={form.watch("discountType")}
                    onValueChange={(value) =>
                      form.setValue("discountType", value as DiscountType, {
                        shouldValidate: true,
                      })
                    }
                  >
                    <SelectTrigger
                      className={cn(
                        errors.discountType && "border-destructive"
                      )}
                    >
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(DiscountType).map((type) => (
                        <SelectItem key={type} value={type}>
                          {type
                            .replace(/_/g, " ")
                            .toLowerCase()
                            .replace(/\b\w/g, (l) => l.toUpperCase())}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.discountType && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.discountType.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Discount Value</Label>
                  <Input
                    {...form.register("discountValue")}
                    name="discountValue"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className={cn(errors.discountValue && "border-destructive")}
                  />
                  {errors.discountValue && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.discountValue.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Discount Amount</Label>
                  <Input
                    {...form.register("discountAmount")}
                    name="discountAmount"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className={cn(
                      errors.discountAmount && "border-destructive"
                    )}
                  />
                  {errors.discountAmount && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.discountAmount.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Tax Type</Label>
                  <Select
                    value={form.watch("taxType")}
                    onValueChange={(value) =>
                      form.setValue("taxType", value as TaxType, {
                        shouldValidate: true,
                      })
                    }
                  >
                    <SelectTrigger
                      className={cn(errors.taxType && "border-destructive")}
                    >
                      <SelectValue placeholder="Select tax type" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(TaxType).map((type) => (
                        <SelectItem key={type} value={type}>
                          {type
                            .replace(/_/g, " ")
                            .toLowerCase()
                            .replace(/\b\w/g, (l) => l.toUpperCase())}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.taxType && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.taxType.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Tax Amount</Label>
                  <Input
                    {...form.register("taxAmount")}
                    name="taxAmount"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className={cn(errors.taxAmount && "border-destructive")}
                  />
                  {errors.taxAmount && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.taxAmount.message}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "notes-info",
      title: "Notes & Cancellation",
      icon: <FileText className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasNotesErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Notes & Cancellation</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Notes</Label>
                <Textarea
                  {...form.register("notes")}
                  name="notes"
                  placeholder="Enter any additional notes..."
                  className={cn(errors.notes && "border-destructive")}
                  rows={3}
                />
                {errors.notes && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.notes.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Cancellation Reason</Label>
                <Textarea
                  {...form.register("cancellationReason")}
                  name="cancellationReason"
                  placeholder="Enter cancellation reason if applicable..."
                  className={cn(
                    errors.cancellationReason && "border-destructive"
                  )}
                  rows={2}
                />
                {errors.cancellationReason && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.cancellationReason.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Cancellation Date</Label>
                <Input
                  {...form.register("cancellationDate")}
                  name="cancellationDate"
                  type="date"
                  className={cn(
                    errors.cancellationDate && "border-destructive"
                  )}
                />
                {errors.cancellationDate && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.cancellationDate.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<PackageReservationTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={packageReservation}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Package Reservation"
      sections={sections}
      onSubmit={handleSubmit(onSubmit)}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingPackageReservation}
    />
  );
}
