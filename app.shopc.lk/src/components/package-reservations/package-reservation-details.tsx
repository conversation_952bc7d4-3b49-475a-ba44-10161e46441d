"use client";

import * as React from "react";
import { useMediaQuery } from "@/hooks/use-media-query";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Calendar,
  Users,
  DollarSign,
  FileText,
  Package,
  Hash,
  MapPin,
  Clock,
  Mail,
  Phone,
} from "lucide-react";
import { usePackageReservationData } from "@/lib/package-reservations/hooks";
import {
  PackageReservationTableData,
  PackageReservationStatus,
  PaymentStatus,
} from "@/types/package-reservation";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { PackageReservationStatusBadge } from "./package-reservation-status-badge";
import { PaymentStatusBadge } from "./payment-status-badge";

interface PackageReservationDetailsProps {
  packageReservation: PackageReservationTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  isDemo?: boolean;
}

export function PackageReservationDetails({
  packageReservation,
  open,
  onOpenChange,
  isDemo = false,
}: PackageReservationDetailsProps) {
  const isDesktop = useMediaQuery("(min-width: 768px)");

  // Fetch complete package reservation data
  const { data: fullPackageReservationResponse, isLoading: isLoadingPackageReservation } =
    usePackageReservationData(packageReservation?.id || "", isDemo);
  const fullPackageReservation = fullPackageReservationResponse?.data;

  const getStatusColor = (status: PackageReservationStatus) => {
    switch (status) {
      case PackageReservationStatus.INQUIRY:
        return "bg-blue-100 text-blue-800";
      case PackageReservationStatus.PENDING:
        return "bg-yellow-100 text-yellow-800";
      case PackageReservationStatus.CONFIRMED:
        return "bg-green-100 text-green-800";
      case PackageReservationStatus.PARTIALLY_PAID:
        return "bg-orange-100 text-orange-800";
      case PackageReservationStatus.PAID:
        return "bg-emerald-100 text-emerald-800";
      case PackageReservationStatus.IN_PROGRESS:
        return "bg-purple-100 text-purple-800";
      case PackageReservationStatus.COMPLETED:
        return "bg-green-100 text-green-800";
      case PackageReservationStatus.CANCELLED:
        return "bg-red-100 text-red-800";
      case PackageReservationStatus.NO_SHOW:
        return "bg-gray-100 text-gray-800";
      case PackageReservationStatus.REFUNDED:
        return "bg-indigo-100 text-indigo-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Content component
  const Content = () => {
    if (isLoadingPackageReservation) {
      return (
        <div className="space-y-6 p-6">
          <div className="space-y-3">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-1/3" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-16 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      );
    }

    if (!fullPackageReservation) {
      return (
        <div className="p-6 text-center">
          <p className="text-muted-foreground">Package reservation not found</p>
        </div>
      );
    }

    return (
      <ScrollArea className="h-full max-h-[80vh] overflow-y-auto">
        <div className="space-y-6 p-6">
          {/* Header */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">{fullPackageReservation.reservationNumber}</h2>
              <div className="flex items-center gap-2">
                <PackageReservationStatusBadge status={fullPackageReservation.status} />
                <PaymentStatusBadge status={fullPackageReservation.paymentStatus} />
              </div>
            </div>
            {fullPackageReservation.referenceNumber && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Hash className="h-4 w-4" />
                <span>Reference: {fullPackageReservation.referenceNumber}</span>
              </div>
            )}
          </div>

          <Separator />

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <Package className="h-5 w-5 text-muted-foreground" />
                  <h3 className="font-semibold">Package Details</h3>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm text-muted-foreground">Package</p>
                  <p className="font-medium">{fullPackageReservation.packageName || "N/A"}</p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Check-in</p>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <p className="font-medium">
                        {format(new Date(fullPackageReservation.startDate), "MMM dd, yyyy")}
                      </p>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Check-out</p>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <p className="font-medium">
                        {format(new Date(fullPackageReservation.endDate), "MMM dd, yyyy")}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-muted-foreground" />
                  <h3 className="font-semibold">Guest Information</h3>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm text-muted-foreground">Primary Guest</p>
                  <p className="font-medium">{fullPackageReservation.primaryGuestName || "N/A"}</p>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Total</p>
                    <p className="font-medium">{fullPackageReservation.totalNumberOfGuests}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Adults</p>
                    <p className="font-medium">{fullPackageReservation.numberOfAdults}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Children</p>
                    <p className="font-medium">{fullPackageReservation.numberOfChildren}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Pricing Information */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-muted-foreground" />
                <h3 className="font-semibold">Pricing Details</h3>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Subtotal</p>
                  <p className="font-medium">${fullPackageReservation.subtotal}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total</p>
                  <p className="font-medium text-lg">${fullPackageReservation.total}</p>
                </div>
                {fullPackageReservation.depositPaid && (
                  <div>
                    <p className="text-sm text-muted-foreground">Deposit Paid</p>
                    <p className="font-medium text-green-600">${fullPackageReservation.depositPaid}</p>
                  </div>
                )}
                {fullPackageReservation.balanceDue && (
                  <div>
                    <p className="text-sm text-muted-foreground">Balance Due</p>
                    <p className="font-medium text-orange-600">${fullPackageReservation.balanceDue}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Additional Information */}
          {(fullPackageReservation.notes || 
            fullPackageReservation.cancellationReason || 
            fullPackageReservation.reservationSource) && (
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                  <h3 className="font-semibold">Additional Information</h3>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {fullPackageReservation.reservationSource && (
                  <div>
                    <p className="text-sm text-muted-foreground">Reservation Source</p>
                    <p className="font-medium">
                      {fullPackageReservation.reservationSource.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                    </p>
                  </div>
                )}
                {fullPackageReservation.notes && (
                  <div>
                    <p className="text-sm text-muted-foreground">Notes</p>
                    <p className="text-sm">{fullPackageReservation.notes}</p>
                  </div>
                )}
                {fullPackageReservation.cancellationReason && (
                  <div>
                    <p className="text-sm text-muted-foreground">Cancellation Reason</p>
                    <p className="text-sm text-red-600">{fullPackageReservation.cancellationReason}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Timestamps */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-muted-foreground" />
                <h3 className="font-semibold">Timeline</h3>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Created</p>
                  <p className="font-medium">
                    {format(new Date(fullPackageReservation.createdAt), "MMM dd, yyyy 'at' h:mm a")}
                  </p>
                </div>
                <div>
                  <p className="text-muted-foreground">Last Updated</p>
                  <p className="font-medium">
                    {format(new Date(fullPackageReservation.updatedAt), "MMM dd, yyyy 'at' h:mm a")}
                  </p>
                </div>
                {fullPackageReservation.confirmationSentAt && (
                  <div>
                    <p className="text-muted-foreground">Confirmation Sent</p>
                    <p className="font-medium">
                      {format(new Date(fullPackageReservation.confirmationSentAt), "MMM dd, yyyy 'at' h:mm a")}
                    </p>
                  </div>
                )}
                {fullPackageReservation.reminderSentAt && (
                  <div>
                    <p className="text-muted-foreground">Reminder Sent</p>
                    <p className="font-medium">
                      {format(new Date(fullPackageReservation.reminderSentAt), "MMM dd, yyyy 'at' h:mm a")}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </ScrollArea>
    );
  };

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Package Reservation Details</DialogTitle>
          </DialogHeader>
          <Content />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent className="max-h-[90vh]">
        <DrawerHeader>
          <DrawerTitle>Package Reservation Details</DrawerTitle>
        </DrawerHeader>
        <Content />
      </DrawerContent>
    </Drawer>
  );
}
