"use client";

import { Badge } from "@/components/ui/badge";
import { PackageReservationStatus } from "@/types/package-reservation";
import { cn } from "@/lib/utils";

interface PackageReservationStatusBadgeProps {
  status: PackageReservationStatus;
  className?: string;
}

export function PackageReservationStatusBadge({
  status,
  className,
}: PackageReservationStatusBadgeProps) {
  const getStatusColor = (status: PackageReservationStatus) => {
    switch (status) {
      case PackageReservationStatus.INQUIRY:
        return "bg-blue-100 text-blue-800 hover:bg-blue-100";
      case PackageReservationStatus.PENDING:
        return "bg-yellow-100 text-yellow-800 hover:bg-yellow-100";
      case PackageReservationStatus.CONFIRMED:
        return "bg-green-100 text-green-800 hover:bg-green-100";
      case PackageReservationStatus.PARTIALLY_PAID:
        return "bg-orange-100 text-orange-800 hover:bg-orange-100";
      case PackageReservationStatus.PAID:
        return "bg-emerald-100 text-emerald-800 hover:bg-emerald-100";
      case PackageReservationStatus.IN_PROGRESS:
        return "bg-purple-100 text-purple-800 hover:bg-purple-100";
      case PackageReservationStatus.COMPLETED:
        return "bg-green-100 text-green-800 hover:bg-green-100";
      case PackageReservationStatus.CANCELLED:
        return "bg-red-100 text-red-800 hover:bg-red-100";
      case PackageReservationStatus.NO_SHOW:
        return "bg-gray-100 text-gray-800 hover:bg-gray-100";
      case PackageReservationStatus.REFUNDED:
        return "bg-indigo-100 text-indigo-800 hover:bg-indigo-100";
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-100";
    }
  };

  const getStatusLabel = (status: PackageReservationStatus) => {
    switch (status) {
      case PackageReservationStatus.INQUIRY:
        return "Inquiry";
      case PackageReservationStatus.PENDING:
        return "Pending";
      case PackageReservationStatus.CONFIRMED:
        return "Confirmed";
      case PackageReservationStatus.PARTIALLY_PAID:
        return "Partially Paid";
      case PackageReservationStatus.PAID:
        return "Paid";
      case PackageReservationStatus.IN_PROGRESS:
        return "In Progress";
      case PackageReservationStatus.COMPLETED:
        return "Completed";
      case PackageReservationStatus.CANCELLED:
        return "Cancelled";
      case PackageReservationStatus.NO_SHOW:
        return "No Show";
      case PackageReservationStatus.REFUNDED:
        return "Refunded";
      default:
        return status;
    }
  };

  return (
    <Badge
      className={cn(
        "font-medium",
        getStatusColor(status),
        className
      )}
    >
      {getStatusLabel(status)}
    </Badge>
  );
}
