"use client";

import * as React from "react";
import type { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye, Calendar, Users, DollarSign } from "lucide-react";
import { PackageReservationTableData, PackageReservationStatus, PaymentStatus } from "@/types/package-reservation";
import { PackageReservationStatusBadge } from "./package-reservation-status-badge";
import { PaymentStatusBadge } from "./payment-status-badge";
import { format } from "date-fns";

export interface ColumnsProps {
  setRowAction: (rowAction: {
    type: "view" | "update" | "delete";
    row: any;
  }) => void;
  isDemo?: boolean;
  isActionsDisabled?: boolean;
  onRefresh?: () => void;
  onStatusUpdate?: (packageReservationId: string, newStatus: PackageReservationStatus) => void;
}

export function getColumns({
  setRowAction,
  isDemo = false,
  isActionsDisabled = false,
  onRefresh,
  onStatusUpdate,
}: ColumnsProps) {
  const columns: ColumnDef<PackageReservationTableData>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 10,
    },
    {
      accessorKey: "reservationNumber",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Reservation #" />
      ),
      cell: ({ row }) => {
        const reservationNumber = row.getValue("reservationNumber") as string;
        return (
          <div className="font-medium">
            {reservationNumber}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "packageName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Package" />
      ),
      cell: ({ row }) => {
        const packageName = row.getValue("packageName") as string;
        return (
          <div className="max-w-[200px] truncate font-medium">
            {packageName}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "primaryGuestName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Primary Guest" />
      ),
      cell: ({ row }) => {
        const primaryGuestName = row.getValue("primaryGuestName") as string;
        return (
          <div className="max-w-[150px] truncate">
            {primaryGuestName || "N/A"}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "startDate",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Check-in" />
      ),
      cell: ({ row }) => {
        const startDate = row.getValue("startDate") as Date;
        return (
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">
              {format(new Date(startDate), "MMM dd, yyyy")}
            </span>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "endDate",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Check-out" />
      ),
      cell: ({ row }) => {
        const endDate = row.getValue("endDate") as Date;
        return (
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">
              {format(new Date(endDate), "MMM dd, yyyy")}
            </span>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "totalNumberOfGuests",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Guests" />
      ),
      cell: ({ row }) => {
        const totalGuests = row.getValue("totalNumberOfGuests") as number;
        const numberOfAdults = row.getValue("numberOfAdults") as number;
        const numberOfChildren = row.getValue("numberOfChildren") as number;
        return (
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">
              {totalGuests} ({numberOfAdults}A{numberOfChildren > 0 ? `, ${numberOfChildren}C` : ""})
            </span>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const status = row.getValue("status") as PackageReservationStatus;
        return <PackageReservationStatusBadge status={status} />;
      },
      enableSorting: true,
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "paymentStatus",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Payment" />
      ),
      cell: ({ row }) => {
        const paymentStatus = row.getValue("paymentStatus") as PaymentStatus;
        return <PaymentStatusBadge status={paymentStatus} />;
      },
      enableSorting: true,
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "total",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Total" />
      ),
      cell: ({ row }) => {
        const total = row.getValue("total") as string;
        return (
          <div className="flex items-center gap-2 font-medium">
            <DollarSign className="h-4 w-4 text-muted-foreground" />
            <span>${total}</span>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Created" />
      ),
      cell: ({ row }) => {
        const createdAt = row.getValue("createdAt") as Date;
        return (
          <div className="text-sm text-muted-foreground">
            {format(new Date(createdAt), "MMM dd, yyyy")}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      id: "actions",
      header: () => <div className="w-8"></div>,
      cell: ({ row }) => {
        const packageReservation = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
                disabled={isActionsDisabled}
              >
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
              <DropdownMenuItem
                onClick={() => setRowAction({ type: "view", row })}
              >
                <Eye className="mr-2 h-4 w-4" />
                View
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => setRowAction({ type: "update", row })}
              >
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => setRowAction({ type: "delete", row })}
                className="text-destructive focus:text-destructive"
              >
                Delete
                <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableSorting: false,
      enableHiding: false,
      size: 10,
    },
  ];

  return columns;
}
