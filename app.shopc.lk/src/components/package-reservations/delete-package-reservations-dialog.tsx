"use client";

import * as React from "react";
import { toast } from "sonner";
import { Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { PackageReservationTableData } from "@/types/package-reservation";
import { useDeletePackageReservation, useBulkDeletePackageReservations } from "@/lib/package-reservations/hooks";
import { ApiStatus } from "@/types/common";

interface DeletePackageReservationsDialogProps {
  packageReservations: PackageReservationTableData[];
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void;
  showTrigger?: boolean;
  isDemo?: boolean;
}

export function DeletePackageReservationsDialog({
  packageReservations,
  open,
  onOpenChange,
  onSuccess,
  showTrigger = true,
  isDemo = false,
}: DeletePackageReservationsDialogProps) {
  const [isDeleting, setIsDeleting] = React.useState(false);

  const deletePackageReservationMutation = useDeletePackageReservation(isDemo);
  const bulkDeletePackageReservationsMutation = useBulkDeletePackageReservations(isDemo);

  const isMultiple = packageReservations.length > 1;
  const packageReservationText = isMultiple
    ? `${packageReservations.length} package reservations`
    : `package reservation "${packageReservations[0]?.reservationNumber}"`;

  const handleDelete = async () => {
    if (packageReservations.length === 0) return;

    setIsDeleting(true);
    try {
      if (isMultiple) {
        // Bulk delete
        const packageReservationIds = packageReservations.map((pr) => pr.id);
        const response = await bulkDeletePackageReservationsMutation.mutateAsync({
          packageReservationIds,
        });

        if (response.status === ApiStatus.SUCCESS) {
          toast.success(
            `Successfully deleted ${packageReservations.length} package reservations`
          );
          onSuccess?.();
          onOpenChange?.(false);
        } else {
          toast.error(response.message || "Failed to delete package reservations");
        }
      } else {
        // Single delete
        const response = await deletePackageReservationMutation.mutateAsync(
          packageReservations[0].id
        );

        if (response.status === ApiStatus.SUCCESS) {
          toast.success("Package reservation deleted successfully");
          onSuccess?.();
          onOpenChange?.(false);
        } else {
          toast.error(response.message || "Failed to delete package reservation");
        }
      }
    } catch (error) {
      console.error("Failed to delete package reservation(s):", error);
      toast.error("Failed to delete package reservation(s)");
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {showTrigger && (
        <DialogTrigger asChild>
          <Button variant="outline" size="sm">
            <Trash2 className="mr-2 h-4 w-4" />
            Delete {isMultiple ? `(${packageReservations.length})` : ""}
          </Button>
        </DialogTrigger>
      )}
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete {isMultiple ? "Package Reservations" : "Package Reservation"}</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete {packageReservationText}? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange?.(false)}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting ? "Deleting..." : "Delete"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
