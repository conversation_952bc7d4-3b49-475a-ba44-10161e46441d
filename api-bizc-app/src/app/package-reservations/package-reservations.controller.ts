import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import { PackageReservationsService } from './package-reservations.service';
import { CreatePackageReservationDto } from './dto/create-package-reservation.dto';
import { UpdatePackageReservationDto } from './dto/update-package-reservation.dto';
import { PackageReservationDto } from './dto/package-reservation.dto';
import { PackageReservationSlimDto } from './dto/package-reservation-slim.dto';
import { PackageReservationIdResponseDto } from './dto/package-reservation-id-response.dto';
import { BulkPackageReservationIdsResponseDto } from './dto/bulk-package-reservation-ids-response.dto';
import { BulkCreatePackageReservationDto } from './dto/bulk-create-package-reservation.dto';
import { DeletePackageReservationResponseDto } from './dto/delete-package-reservation-response.dto';
import { BulkDeletePackageReservationDto } from './dto/bulk-delete-package-reservation.dto';
import { BulkDeletePackageReservationResponseDto } from './dto/bulk-delete-package-reservation-response.dto';
import { PaginatedPackageReservationsResponseDto } from './dto/paginated-package-reservations-response.dto';
import {
  BulkUpdatePackageReservationStatusDto,
  BulkUpdatePackageReservationStatusResponseDto,
} from './dto/bulk-update-package-reservation-status.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { PackageReservationNumberAvailabilityResponseDto } from './dto/check-package-reservation-number.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';
import {
  CheckPackageAvailabilityDto,
  CheckPackageAvailabilityResponseDto,
} from './dto/check-package-availability.dto';
import {
  CalculatePackagePricingDto,
  CalculatePackagePricingResponseDto,
} from './dto/calculate-package-pricing.dto';
import {
  ConfirmPackageReservationDto,
  ConfirmPackageReservationResponseDto,
} from './dto/confirm-package-reservation.dto';
import {
  CancelPackageReservationDto,
  CancelPackageReservationResponseDto,
} from './dto/cancel-package-reservation.dto';
import {
  AddGuestToPackageReservationDto,
  RemoveGuestFromPackageReservationDto,
  PackageReservationGuestResponseDto,
} from './dto/package-guest-management.dto';
import {
  SendPackageConfirmationDto,
  SendPackageReminderDto,
  SendPackageConfirmationResponseDto,
  SendPackageReminderResponseDto,
} from './dto/package-communication.dto';

@ApiTags('package-reservations')
@Controller('package-reservations')
@UseGuards(PermissionsGuard)
export class PackageReservationsController {
  constructor(
    private readonly packageReservationsService: PackageReservationsService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_CREATE)
  @ApiOperation({ summary: 'Create a new package reservation' })
  @ApiBody({
    description: 'Package reservation creation data',
    type: CreatePackageReservationDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The package reservation has been successfully created',
    type: PackageReservationIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Reservation number already exists',
  })
  create(
    @Request() req,
    @Body() createPackageReservationDto: CreatePackageReservationDto,
  ): Promise<PackageReservationIdResponseDto> {
    return this.packageReservationsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createPackageReservationDto,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_CREATE)
  @ApiOperation({ summary: 'Bulk create package reservations' })
  @ApiBody({
    description: 'Bulk package reservation creation data',
    type: BulkCreatePackageReservationDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The package reservations have been successfully created',
    type: BulkPackageReservationIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or duplicate reservation numbers',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Reservation numbers already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreatePackageReservationDto: BulkCreatePackageReservationDto,
  ): Promise<BulkPackageReservationIdsResponseDto> {
    return this.packageReservationsService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreatePackageReservationDto.packageReservations,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_READ)
  @ApiOperation({
    summary: 'Get all package reservations for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter from date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter to date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'reservationNumber',
    description: 'Filter by reservation number',
    required: false,
    type: String,
    example: 'PKG-2024-001',
  })
  @ApiQuery({
    name: 'referenceNumber',
    description: 'Filter by reference number',
    required: false,
    type: String,
    example: 'REF-001',
  })
  @ApiQuery({
    name: 'packageId',
    description: 'Filter by package ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description:
      'Filter by status (comma-separated for multiple values). Supports URL encoding: status=PENDING%2CCONFIRMED',
    required: false,
    type: String,
    example: 'PENDING,CONFIRMED',
  })
  @ApiQuery({
    name: 'paymentStatus',
    description:
      'Filter by payment status (comma-separated for multiple values). Supports URL encoding: paymentStatus=PENDING%2CPAID',
    required: false,
    type: String,
    example: 'PENDING,PAID',
  })
  @ApiQuery({
    name: 'filters',
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty)',
    required: false,
    type: String,
    example:
      '[{"id":"reservationNumber","value":"PKG","operator":"iLike","type":"text","rowId":"1"},{"id":"status","value":"CONFIRMED","operator":"eq","type":"select","rowId":"2"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    type: String,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: reservationNumber, startDate, endDate, status, paymentStatus, total, createdAt, updatedAt',
    required: false,
    type: String,
    example: '[{"id":"startDate","desc":false}]',
  })
  @ApiResponse({
    status: 200,
    description:
      "Returns all package reservations for the user's active business with pagination",
    type: PaginatedPackageReservationsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('reservationNumber') reservationNumber?: string,
    @Query('referenceNumber') referenceNumber?: string,
    @Query('packageId') packageId?: string,
    @Query('status') status?: string,
    @Query('paymentStatus') paymentStatus?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedPackageReservationsResponseDto> {
    return this.packageReservationsService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      reservationNumber,
      referenceNumber,
      packageId,
      status,
      paymentStatus,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('check-reservation-number-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_READ)
  @ApiOperation({ summary: 'Check if a reservation number is available' })
  @ApiQuery({
    name: 'reservationNumber',
    description: 'Reservation number to check',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the reservation number is available',
    type: PackageReservationNumberAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkReservationNumberAvailability(
    @Request() req,
    @Query('reservationNumber') reservationNumber: string,
  ): Promise<{ available: boolean }> {
    return this.packageReservationsService.checkReservationNumberAvailability(
      req.user.id,
      req.user.activeBusinessId,
      reservationNumber,
    );
  }

  @Get('check-reference-number-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_READ)
  @ApiOperation({ summary: 'Check if a reference number is available' })
  @ApiQuery({
    name: 'referenceNumber',
    required: true,
    description: 'The reference number to check for availability',
    example: 'REF-001',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns reference number availability',
    type: PackageReservationNumberAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkReferenceNumberAvailability(
    @Request() req,
    @Query('referenceNumber') referenceNumber: string,
  ): Promise<{ available: boolean }> {
    return this.packageReservationsService.checkReferenceNumberAvailability(
      req.user.id,
      req.user.activeBusinessId,
      referenceNumber,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_READ)
  @ApiOperation({ summary: 'Get all package reservations in slim format' })
  @ApiResponse({
    status: 200,
    description: 'All package reservations returned successfully',
    type: [PackageReservationSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<PackageReservationSlimDto[]> {
    return this.packageReservationsService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_READ)
  @ApiOperation({ summary: 'Get a package reservation by ID' })
  @ApiParam({
    name: 'id',
    description: 'Package reservation ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the package reservation with guest details',
    type: PackageReservationDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Package reservation not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this package reservation',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findOne(
    @Request() req,
    @Param('id') id: string,
  ): Promise<PackageReservationDto> {
    return this.packageReservationsService.findOne(req.user.id, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Update a package reservation' })
  @ApiParam({
    name: 'id',
    description: 'Package reservation ID',
  })
  @ApiBody({
    description: 'Package reservation update data',
    type: UpdatePackageReservationDto,
  })
  @ApiResponse({
    status: 200,
    description: 'The package reservation has been successfully updated',
    type: PackageReservationIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 404,
    description: 'Package reservation not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this package reservation',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Reservation number already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updatePackageReservationDto: UpdatePackageReservationDto,
  ): Promise<PackageReservationIdResponseDto> {
    return this.packageReservationsService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updatePackageReservationDto,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_DELETE)
  @ApiOperation({ summary: 'Bulk delete package reservations' })
  @ApiBody({
    description: 'Array of package reservation IDs to delete',
    type: BulkDeletePackageReservationDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Package reservations have been successfully deleted',
    type: BulkDeletePackageReservationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or package reservations not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more package reservations not found',
  })
  async bulkDelete(
    @Request() req,
    @Body() bulkDeletePackageReservationDto: BulkDeletePackageReservationDto,
  ): Promise<BulkDeletePackageReservationResponseDto> {
    return this.packageReservationsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeletePackageReservationDto.packageReservationIds,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_DELETE)
  @ApiOperation({ summary: 'Delete a package reservation' })
  @ApiParam({
    name: 'id',
    description: 'Package reservation ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The package reservation has been successfully deleted',
    type: DeletePackageReservationResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Package reservation not found',
  })
  @ApiResponse({
    status: 401,
    description:
      'Unauthorized - Access denied to delete this package reservation',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
  ): Promise<DeletePackageReservationResponseDto> {
    return this.packageReservationsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch('bulk-status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Bulk update package reservation status' })
  @ApiBody({
    description: 'Array of package reservation IDs and status to update',
    type: BulkUpdatePackageReservationStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Package reservation status has been successfully updated',
    type: BulkUpdatePackageReservationStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or package reservations not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkUpdateStatus(
    @Request() req,
    @Body() bulkUpdateStatusDto: BulkUpdatePackageReservationStatusDto,
  ): Promise<BulkUpdatePackageReservationStatusResponseDto> {
    const result =
      await this.packageReservationsService.bulkUpdatePackageReservationStatus(
        req.user.id,
        req.user.activeBusinessId,
        bulkUpdateStatusDto.packageReservationIds,
        bulkUpdateStatusDto.status,
      );

    return {
      updated: result.updated,
      message: `Successfully updated status for ${result.updated} package reservations`,
      updatedIds: result.updatedIds,
      failed: result.failed.length > 0 ? result.failed : undefined,
    };
  }

  @Post('check-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_READ)
  @ApiOperation({
    summary: 'Check package availability for given dates and guests',
  })
  @ApiBody({
    description: 'Package availability check parameters',
    type: CheckPackageAvailabilityDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns available packages for the specified criteria',
    type: CheckPackageAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkAvailability(
    @Request() req,
    @Body() checkAvailabilityDto: CheckPackageAvailabilityDto,
  ): Promise<CheckPackageAvailabilityResponseDto> {
    return this.packageReservationsService.checkPackageAvailability(
      req.user.id,
      req.user.activeBusinessId,
      checkAvailabilityDto.startDate,
      checkAvailabilityDto.endDate,
      checkAvailabilityDto.adults,
      checkAvailabilityDto.children,
      checkAvailabilityDto.packageIds,
    );
  }

  @Post('calculate-pricing')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_READ)
  @ApiOperation({ summary: 'Calculate pricing for a package reservation' })
  @ApiBody({
    description: 'Package pricing calculation parameters',
    type: CalculatePackagePricingDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns detailed pricing breakdown for the package',
    type: CalculatePackagePricingResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Package not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  calculatePricing(
    @Request() req,
    @Body() calculatePricingDto: CalculatePackagePricingDto,
  ): Promise<CalculatePackagePricingResponseDto> {
    return this.packageReservationsService.calculatePackagePricing(
      req.user.id,
      req.user.activeBusinessId,
      calculatePricingDto.packageId,
      calculatePricingDto.startDate,
      calculatePricingDto.endDate,
      calculatePricingDto.adults,
      calculatePricingDto.children,
      calculatePricingDto.discountType,
      calculatePricingDto.discountValue,
      calculatePricingDto.taxType,
    );
  }

  @Patch(':id/confirm')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Confirm a package reservation' })
  @ApiParam({
    name: 'id',
    description: 'Package reservation ID',
  })
  @ApiBody({
    description: 'Package reservation confirmation data',
    type: ConfirmPackageReservationDto,
  })
  @ApiResponse({
    status: 200,
    description: 'The package reservation has been successfully confirmed',
    type: ConfirmPackageReservationResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Package reservation not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this package reservation',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  confirmReservation(
    @Request() req,
    @Param('id') id: string,
    @Body() confirmReservationDto: ConfirmPackageReservationDto,
  ): Promise<ConfirmPackageReservationResponseDto> {
    return this.packageReservationsService.confirmPackageReservation(
      req.user.id,
      req.user.activeBusinessId,
      id,
      confirmReservationDto,
    );
  }

  @Patch(':id/cancel')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Cancel a package reservation' })
  @ApiParam({
    name: 'id',
    description: 'Package reservation ID',
  })
  @ApiBody({
    description: 'Package reservation cancellation data',
    type: CancelPackageReservationDto,
  })
  @ApiResponse({
    status: 200,
    description: 'The package reservation has been successfully cancelled',
    type: CancelPackageReservationResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Package reservation not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this package reservation',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  cancelReservation(
    @Request() req,
    @Param('id') id: string,
    @Body() cancelReservationDto: CancelPackageReservationDto,
  ): Promise<CancelPackageReservationResponseDto> {
    return this.packageReservationsService.cancelPackageReservation(
      req.user.id,
      req.user.activeBusinessId,
      id,
      cancelReservationDto,
    );
  }

  @Post(':id/guests')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Add guest to package reservation' })
  @ApiParam({
    name: 'id',
    description: 'Package reservation ID',
  })
  @ApiBody({
    description: 'Guest details to add',
    type: AddGuestToPackageReservationDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Guest added to reservation successfully',
    type: PackageReservationGuestResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid guest data or guest already associated',
  })
  @ApiResponse({
    status: 404,
    description: 'Package reservation or guest not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Guest already associated with reservation',
  })
  addGuest(
    @Request() req,
    @Param('id') id: string,
    @Body() addGuestDto: AddGuestToPackageReservationDto,
  ): Promise<PackageReservationGuestResponseDto> {
    return this.packageReservationsService.addGuestToReservation(
      req.user.id,
      req.user.activeBusinessId,
      id,
      addGuestDto,
    );
  }

  @Delete(':id/guests/:guestId')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Remove guest from package reservation' })
  @ApiParam({
    name: 'id',
    description: 'Package reservation ID',
  })
  @ApiParam({
    name: 'guestId',
    description: 'Guest ID to remove',
  })
  @ApiBody({
    description: 'Removal details',
    type: RemoveGuestFromPackageReservationDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Guest removed from reservation successfully',
    type: PackageReservationGuestResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Cannot remove primary guest or last guest',
  })
  @ApiResponse({
    status: 404,
    description: 'Package reservation or guest not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  removeGuest(
    @Request() req,
    @Param('id') id: string,
    @Param('guestId') guestId: string,
    @Body() removeGuestDto: RemoveGuestFromPackageReservationDto,
  ): Promise<PackageReservationGuestResponseDto> {
    return this.packageReservationsService.removeGuestFromReservation(
      req.user.id,
      req.user.activeBusinessId,
      id,
      guestId,
      removeGuestDto,
    );
  }

  @Post(':id/send-confirmation')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_UPDATE)
  @ApiOperation({
    summary: 'Send confirmation email/SMS for package reservation',
  })
  @ApiParam({
    name: 'id',
    description: 'Package reservation ID',
  })
  @ApiBody({
    description: 'Confirmation sending options',
    type: SendPackageConfirmationDto,
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Package confirmation sent successfully',
    type: SendPackageConfirmationResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Package reservation not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  sendConfirmation(
    @Request() req,
    @Param('id') id: string,
    @Body() sendConfirmationDto: SendPackageConfirmationDto,
  ): Promise<SendPackageConfirmationResponseDto> {
    return this.packageReservationsService.sendPackageConfirmation(
      req.user.id,
      req.user.activeBusinessId,
      id,
      sendConfirmationDto,
    );
  }

  @Post(':id/send-reminder')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PACKAGE_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Send reminder email/SMS for package reservation' })
  @ApiParam({
    name: 'id',
    description: 'Package reservation ID',
  })
  @ApiBody({
    description: 'Reminder sending options',
    type: SendPackageReminderDto,
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Package reminder sent successfully',
    type: SendPackageReminderResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Package reservation not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  sendReminder(
    @Request() req,
    @Param('id') id: string,
    @Body() sendReminderDto: SendPackageReminderDto,
  ): Promise<SendPackageReminderResponseDto> {
    return this.packageReservationsService.sendPackageReminder(
      req.user.id,
      req.user.activeBusinessId,
      id,
      sendReminderDto,
    );
  }
}
