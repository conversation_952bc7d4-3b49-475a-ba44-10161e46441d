import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreatePackageReservationDto } from './dto/create-package-reservation.dto';
import { UpdatePackageReservationDto } from './dto/update-package-reservation.dto';
import { PackageReservationDto } from './dto/package-reservation.dto';
import { PackageReservationSlimDto } from './dto/package-reservation-slim.dto';
import { PackageReservationListDto } from './dto/package-reservation-list.dto';
import {
  packageReservations,
  packageReservationGuests,
} from '../drizzle/schema/package-reservations.schema';
import { packages } from '../drizzle/schema/packages.schema';
import { guests } from '../drizzle/schema/guests.schema';
import { users } from '../drizzle/schema/users.schema';
import { customers } from '../drizzle/schema/customers.schema';
import {
  eq,
  and,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  inArray,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { ActivityLogName, PackageReservationStatus } from '../shared/types';
import { CustomerStatus } from '../shared/types/customer.enum';
import { GuestType, GuestStatus } from '../drizzle/schema/guests.schema';

@Injectable()
export class PackageReservationsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  /**
   * Generate a unique guest number for the business
   */
  private async generateUniqueGuestNumber(businessId: string): Promise<string> {
    const prefix = 'PKG-GUEST-';
    let counter = 1;
    let guestNumber = `${prefix}${counter.toString().padStart(6, '0')}`;

    // Check if guest number already exists
    while (true) {
      const existingGuest = await this.db
        .select({ id: guests.id })
        .from(guests)
        .where(
          and(
            eq(guests.businessId, businessId),
            eq(guests.guestNumber, guestNumber),
            isNull(guests.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingGuest) {
        break;
      }

      counter++;
      guestNumber = `${prefix}${counter.toString().padStart(6, '0')}`;
    }

    return guestNumber;
  }

  /**
   * Generate customer display name from guest DTO
   */
  private generateCustomerDisplayName(guestDto: any): string {
    if (guestDto.customerDisplayName) {
      return guestDto.customerDisplayName;
    }

    const nameParts: string[] = [];
    if (guestDto.title) nameParts.push(guestDto.title);
    if (guestDto.firstName) nameParts.push(guestDto.firstName);
    if (guestDto.middleName) nameParts.push(guestDto.middleName);
    if (guestDto.lastName) nameParts.push(guestDto.lastName);
    if (guestDto.suffix) nameParts.push(guestDto.suffix);

    return nameParts.length > 0 ? nameParts.join(' ') : 'Package Guest';
  }

  /**
   * Create a customer record from guest DTO
   */
  private async createCustomerFromDto(
    tx: any,
    businessId: string,
    userId: string,
    guestDto: any,
  ): Promise<string> {
    const customerDisplayName = this.generateCustomerDisplayName(guestDto);

    // Check if customer with same display name already exists
    const existingCustomer = await tx
      .select({ id: customers.id })
      .from(customers)
      .where(
        and(
          eq(customers.businessId, businessId),
          eq(customers.customerDisplayName, customerDisplayName),
          isNull(customers.deletedAt),
        ),
      );

    if (existingCustomer.length > 0) {
      return existingCustomer[0].id;
    }

    const customerData = {
      businessId,
      customerDisplayName,
      title: guestDto.title,
      firstName: guestDto.firstName,
      middleName: guestDto.middleName,
      lastName: guestDto.lastName,
      suffix: guestDto.suffix,
      companyName: guestDto.companyName,
      email: guestDto.email,
      phoneNumber: guestDto.phoneNumber,
      mobileNumber: guestDto.mobileNumber,
      fax: guestDto.fax,
      website: guestDto.website,
      other: guestDto.other,
      isSubCustomer: guestDto.isSubCustomer ?? false,
      isAllocatedToAllLocations: false, // Default to false for guest-created customers
      status: guestDto.customerStatus ?? CustomerStatus.ACTIVE,
      notes: guestDto.customerNotes,
      createdBy: userId,
      updatedBy: userId,
    };

    const createdCustomer = await tx
      .insert(customers)
      .values(customerData)
      .returning();
    return createdCustomer[0].id;
  }

  /**
   * Create a guest record from guest DTO (with automatic customer creation)
   */
  private async createGuestFromDto(
    tx: any,
    businessId: string,
    userId: string,
    guestDto: any,
  ): Promise<string> {
    // First create the customer record
    const customerId = await this.createCustomerFromDto(
      tx,
      businessId,
      userId,
      guestDto,
    );

    const guestNumber = await this.generateUniqueGuestNumber(businessId);

    const guestData = {
      businessId,
      customerId,
      guestNumber,
      dateOfBirth: guestDto.dateOfBirth ? new Date(guestDto.dateOfBirth) : null,
      gender: guestDto.gender,
      nationality: guestDto.nationality,
      identificationType: guestDto.identificationType,
      identificationNumber: guestDto.identificationNumber,
      identificationIssueDate: guestDto.identificationIssueDate
        ? new Date(guestDto.identificationIssueDate)
        : null,
      identificationExpiryDate: guestDto.identificationExpiryDate
        ? new Date(guestDto.identificationExpiryDate)
        : null,
      identificationIssuingCountry: guestDto.identificationIssuingCountry,
      guestType: guestDto.guestType ?? GuestType.PACKAGE,
      status: guestDto.status ?? GuestStatus.ACTIVE,
      notes: guestDto.notes,
      createdBy: userId,
      updatedBy: userId,
    };

    const createdGuest = await tx.insert(guests).values(guestData).returning();
    return createdGuest[0].id;
  }

  async create(
    userId: string,
    businessId: string | null,
    createPackageReservationDto: CreatePackageReservationDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a reservation with the same number already exists for this business
      const existingReservation = await this.db
        .select()
        .from(packageReservations)
        .where(
          and(
            eq(packageReservations.businessId, businessId),
            ilike(
              packageReservations.reservationNumber,
              createPackageReservationDto.reservationNumber,
            ),
            isNull(packageReservations.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingReservation) {
        throw new ConflictException(
          `A package reservation with the number '${createPackageReservationDto.reservationNumber}' already exists for this business`,
        );
      }

      // Check if reference number exists (if provided)
      if (createPackageReservationDto.referenceNumber) {
        const existingReferenceReservation = await this.db
          .select()
          .from(packageReservations)
          .where(
            and(
              eq(packageReservations.businessId, businessId),
              ilike(
                packageReservations.referenceNumber,
                createPackageReservationDto.referenceNumber,
              ),
              isNull(packageReservations.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (existingReferenceReservation) {
          throw new ConflictException(
            `A package reservation with the reference number '${createPackageReservationDto.referenceNumber}' already exists for this business`,
          );
        }
      }

      // Verify package exists and belongs to the business
      const packageExists = await this.db
        .select()
        .from(packages)
        .where(
          and(
            eq(packages.id, createPackageReservationDto.packageId),
            eq(packages.businessId, businessId),
            isNull(packages.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!packageExists) {
        throw new BadRequestException(
          'Package not found or does not belong to this business',
        );
      }

      // Create the package reservation in a transaction
      const newReservation = await this.db.transaction(async (tx) => {
        const reservation = await tx
          .insert(packageReservations)
          .values({
            businessId,
            reservationNumber: createPackageReservationDto.reservationNumber,
            referenceNumber: createPackageReservationDto.referenceNumber,
            packageId: createPackageReservationDto.packageId,
            startDate: new Date(createPackageReservationDto.startDate),
            endDate: new Date(createPackageReservationDto.endDate),
            totalNumberOfGuests:
              createPackageReservationDto.totalNumberOfGuests,
            numberOfAdults: createPackageReservationDto.numberOfAdults,
            numberOfChildren: createPackageReservationDto.numberOfChildren ?? 0,
            status:
              createPackageReservationDto.status ??
              PackageReservationStatus.PENDING,
            reservationSource: createPackageReservationDto.reservationSource,
            paymentStatus: createPackageReservationDto.paymentStatus,
            subtotal: createPackageReservationDto.subtotal ?? '0.00',
            total: createPackageReservationDto.total ?? '0.00',
            depositPaid: createPackageReservationDto.depositPaid,
            balanceDue: createPackageReservationDto.balanceDue,
            discountType: createPackageReservationDto.discountType,
            discountValue: createPackageReservationDto.discountValue,
            discountAmount: createPackageReservationDto.discountAmount,
            taxType: createPackageReservationDto.taxType,
            notes: createPackageReservationDto.notes,
            cancellationReason: createPackageReservationDto.cancellationReason,
            cancellationDate: createPackageReservationDto.cancellationDate
              ? new Date(createPackageReservationDto.cancellationDate)
              : null,
            confirmationSent:
              createPackageReservationDto.confirmationSent ?? false,
            confirmationSentAt: createPackageReservationDto.confirmationSentAt
              ? new Date(createPackageReservationDto.confirmationSentAt)
              : null,
            reminderSent: createPackageReservationDto.reminderSent ?? false,
            reminderSentAt: createPackageReservationDto.reminderSentAt
              ? new Date(createPackageReservationDto.reminderSentAt)
              : null,
            createdBy: userId,
          })
          .returning();

        return reservation[0];
      });

      // Handle guest associations if provided
      if (
        createPackageReservationDto.guests &&
        createPackageReservationDto.guests.length > 0
      ) {
        await this.manageReservationGuests(
          newReservation.id,
          businessId,
          createPackageReservationDto.guests,
          userId,
        );
      }

      // Log the reservation creation activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Package reservation "${createPackageReservationDto.reservationNumber}" was created`,
        { id: newReservation.id.toString(), type: 'package-reservation' },
        { id: userId, type: 'user' },
        { packageReservationId: newReservation.id, businessId },
      );

      return {
        id: newReservation.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create package reservation: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createPackageReservationDto: CreatePackageReservationDto,
  ): Promise<{ id: string; message: string }> {
    const result = await this.create(
      userId,
      businessId,
      createPackageReservationDto,
    );
    return {
      id: result.id,
      message: 'Package reservation created successfully',
    };
  }

  private async manageReservationGuests(
    reservationId: string,
    businessId: string,
    guestDtos: any[],
    userId: string,
  ): Promise<void> {
    if (!guestDtos || guestDtos.length === 0) {
      return;
    }

    // Validate guest data
    const guestIds = guestDtos
      .map((guest) => guest.guestId)
      .filter((id): id is string => id !== undefined);

    // Validate existing guests exist (if any guestIds provided)
    if (guestIds.length > 0) {
      const existingGuests = await this.db
        .select({ id: guests.id })
        .from(guests)
        .where(
          and(
            inArray(guests.id, guestIds),
            eq(guests.businessId, businessId),
            isNull(guests.deletedAt),
          ),
        );

      if (existingGuests.length !== guestIds.length) {
        throw new BadRequestException(
          'One or more guests do not exist or do not belong to this business',
        );
      }
    }

    // Validate guest data for new guest creation
    for (const guest of guestDtos) {
      if (!guest.guestId) {
        // If no guestId provided, validate customer creation data
        if (!guest.customerDisplayName && !guest.firstName && !guest.lastName) {
          throw new BadRequestException(
            'Either guestId (for existing guest) or customerDisplayName/firstName/lastName (for new guest) must be provided',
          );
        }
      }
    }

    // Create guests and reservation guests in transaction
    await this.db.transaction(async (tx) => {
      const finalGuestIds: string[] = [];
      const primaryGuestMap = new Map<number, string>(); // Map index to guest ID for primary guest references

      // First pass: Handle existing guests or create new guests
      for (let i = 0; i < guestDtos.length; i++) {
        const guestDto = guestDtos[i];
        let guestId: string;

        if (guestDto.guestId) {
          // Use existing guest
          guestId = guestDto.guestId;
        } else {
          // Create new guest
          guestId = await this.createGuestFromDto(
            tx,
            businessId,
            userId,
            guestDto,
          );
        }

        finalGuestIds.push(guestId);

        if (guestDto.isPrimaryGuest) {
          primaryGuestMap.set(i, guestId);
        }
      }

      // Second pass: Create reservation guest relationships
      const reservationGuestData = guestDtos.map((guest, index) => {
        // Find primary guest ID if this guest is not primary
        let primaryGuestId = null;
        if (!guest.isPrimaryGuest && guest.relationshipToPrimary) {
          // For simplicity, use the first primary guest found
          const primaryGuestEntry = Array.from(primaryGuestMap.entries())[0];
          if (primaryGuestEntry) {
            primaryGuestId = primaryGuestEntry[1];
          }
        }

        return {
          reservationId,
          guestId: finalGuestIds[index],
          isPrimaryGuest: guest.isPrimaryGuest ?? false,
          primaryGuestId,
          relationshipToPrimary: guest.relationshipToPrimary,
          participationStatus: guest.participationStatus ?? 'CONFIRMED',
          packageStarted: false,
          packageCompleted: false,
          notes: guest.notes,
          createdBy: userId,
        };
      });

      await tx.insert(packageReservationGuests).values(reservationGuestData);
    });
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    reservationNumber?: string,
    referenceNumber?: string,
    packageId?: string,
    status?: string,
    paymentStatus?: string,
    _filters?: string,
    _joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: PackageReservationListDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      isNull(packageReservations.deletedAt),
      eq(packageReservations.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(packageReservations.startDate, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(packageReservations.endDate, toDate));
      }
    }

    // Add simple filters
    if (reservationNumber) {
      whereConditions.push(
        ilike(packageReservations.reservationNumber, `%${reservationNumber}%`),
      );
    }

    if (referenceNumber) {
      whereConditions.push(
        ilike(packageReservations.referenceNumber, `%${referenceNumber}%`),
      );
    }

    if (packageId) {
      whereConditions.push(eq(packageReservations.packageId, packageId));
    }

    if (status) {
      const statusArray = status
        .split(',')
        .map((s) => s.trim()) as PackageReservationStatus[];
      whereConditions.push(inArray(packageReservations.status, statusArray));
    }

    if (paymentStatus) {
      const paymentStatusArray = paymentStatus
        .split(',')
        .map((s) => s.trim()) as any[];
      whereConditions.push(
        inArray(packageReservations.paymentStatus, paymentStatusArray),
      );
    }

    // Build sort conditions
    let orderBy = [desc(packageReservations.createdAt)];
    if (sort) {
      try {
        const sortConfig = JSON.parse(sort);
        if (Array.isArray(sortConfig) && sortConfig.length > 0) {
          orderBy = sortConfig.map(({ id, desc: isDesc }) => {
            const column = packageReservations[id];
            return column
              ? isDesc
                ? desc(column)
                : asc(column)
              : desc(packageReservations.createdAt);
          });
        }
      } catch {
        // Keep default sort if parsing fails
      }
    }

    // Execute query with joins
    const result = await this.db
      .select({
        id: packageReservations.id,
        businessId: packageReservations.businessId,
        reservationNumber: packageReservations.reservationNumber,
        referenceNumber: packageReservations.referenceNumber,
        packageId: packageReservations.packageId,
        packageName: packages.name,
        packageCode: packages.packageCode,
        startDate: packageReservations.startDate,
        endDate: packageReservations.endDate,
        totalNumberOfGuests: packageReservations.totalNumberOfGuests,
        numberOfAdults: packageReservations.numberOfAdults,
        numberOfChildren: packageReservations.numberOfChildren,
        status: packageReservations.status,
        reservationSource: packageReservations.reservationSource,
        paymentStatus: packageReservations.paymentStatus,
        total: packageReservations.total,
        depositPaid: packageReservations.depositPaid,
        balanceDue: packageReservations.balanceDue,
        confirmationSent: packageReservations.confirmationSent,
        reminderSent: packageReservations.reminderSent,
        createdBy: users.displayName,
        updatedBy: sql<string>`COALESCE(updated_user.display_name, NULL)`,
        createdAt: packageReservations.createdAt,
        updatedAt: packageReservations.updatedAt,
      })
      .from(packageReservations)
      .leftJoin(packages, eq(packageReservations.packageId, packages.id))
      .leftJoin(users, eq(packageReservations.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(packageReservations.updatedBy, sql`updated_user.id`),
      )
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(packageReservations)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      'Viewed all package reservations',
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    return {
      data: result.map((item) => this.mapToPackageReservationListDto(item)),
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    };
  }

  private mapToPackageReservationListDto(row: any): PackageReservationListDto {
    return {
      id: row.id,
      reservationNumber: row.reservationNumber,
      referenceNumber: row.referenceNumber,
      packageId: row.packageId,
      packageName: row.packageName,
      packageCode: row.packageCode,
      startDate: row.startDate,
      endDate: row.endDate,
      totalNumberOfGuests: row.totalNumberOfGuests,
      numberOfAdults: row.numberOfAdults,
      numberOfChildren: row.numberOfChildren,
      status: row.status,
      reservationSource: row.reservationSource,
      paymentStatus: row.paymentStatus,
      total: row.total,
      depositPaid: row.depositPaid,
      balanceDue: row.balanceDue,
      confirmationSent: row.confirmationSent,
      reminderSent: row.reminderSent,
      primaryGuestName: null, // Will be populated separately if needed
      primaryGuestEmail: null,
      primaryGuestPhone: null,
      createdBy: row.createdBy,
      updatedBy: row.updatedBy,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<PackageReservationSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: packageReservations.id,
        reservationNumber: packageReservations.reservationNumber,
        referenceNumber: packageReservations.referenceNumber,
        packageName: packages.name,
        startDate: packageReservations.startDate,
        endDate: packageReservations.endDate,
        totalNumberOfGuests: packageReservations.totalNumberOfGuests,
        status: packageReservations.status,
        total: packageReservations.total,
      })
      .from(packageReservations)
      .leftJoin(packages, eq(packageReservations.packageId, packages.id))
      .where(
        and(
          isNull(packageReservations.deletedAt),
          eq(packageReservations.businessId, businessId),
        ),
      )
      .orderBy(desc(packageReservations.createdAt));

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      'Viewed all package reservations in slim format',
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    return result.map((row) => ({
      id: row.id,
      reservationNumber: row.reservationNumber,
      referenceNumber: row.referenceNumber,
      packageName: row.packageName,
      startDate: row.startDate,
      endDate: row.endDate,
      totalNumberOfGuests: row.totalNumberOfGuests,
      status: row.status,
      total: row.total,
      primaryGuestName: null, // Will be populated separately if needed
    }));
  }

  async findOne(userId: string, id: string): Promise<PackageReservationDto> {
    const result = await this.db
      .select({
        id: packageReservations.id,
        businessId: packageReservations.businessId,
        reservationNumber: packageReservations.reservationNumber,
        referenceNumber: packageReservations.referenceNumber,
        packageId: packageReservations.packageId,
        packageName: packages.name,
        packageCode: packages.packageCode,
        packageType: packages.type,
        packageDescription: packages.description,
        startDate: packageReservations.startDate,
        endDate: packageReservations.endDate,
        totalNumberOfGuests: packageReservations.totalNumberOfGuests,
        numberOfAdults: packageReservations.numberOfAdults,
        numberOfChildren: packageReservations.numberOfChildren,
        status: packageReservations.status,
        reservationSource: packageReservations.reservationSource,
        paymentStatus: packageReservations.paymentStatus,
        subtotal: packageReservations.subtotal,
        total: packageReservations.total,
        depositPaid: packageReservations.depositPaid,
        balanceDue: packageReservations.balanceDue,
        discountType: packageReservations.discountType,
        discountValue: packageReservations.discountValue,
        discountAmount: packageReservations.discountAmount,
        taxType: packageReservations.taxType,
        notes: packageReservations.notes,
        cancellationReason: packageReservations.cancellationReason,
        cancellationDate: packageReservations.cancellationDate,
        confirmationSent: packageReservations.confirmationSent,
        confirmationSentAt: packageReservations.confirmationSentAt,
        reminderSent: packageReservations.reminderSent,
        reminderSentAt: packageReservations.reminderSentAt,
        createdBy: users.displayName,
        updatedBy: sql<string>`COALESCE(updated_user.display_name, NULL)`,
        createdAt: packageReservations.createdAt,
        updatedAt: packageReservations.updatedAt,
      })
      .from(packageReservations)
      .leftJoin(packages, eq(packageReservations.packageId, packages.id))
      .leftJoin(users, eq(packageReservations.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(packageReservations.updatedBy, sql`updated_user.id`),
      )
      .where(
        and(
          eq(packageReservations.id, id),
          isNull(packageReservations.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException('Package reservation not found');
    }

    // Get associated guests
    const guestResults = await this.db
      .select({
        id: packageReservationGuests.id,
        reservationId: packageReservationGuests.reservationId,
        guestId: packageReservationGuests.guestId,
        guestFirstName: sql<string>`customers.first_name`,
        guestLastName: sql<string>`customers.last_name`,
        guestEmail: sql<string>`customers.email`,
        guestPhone: sql<string>`customers.phone_number`,
        isPrimaryGuest: packageReservationGuests.isPrimaryGuest,
        primaryGuestId: packageReservationGuests.primaryGuestId,
        relationshipToPrimary: packageReservationGuests.relationshipToPrimary,
        participationStatus: packageReservationGuests.participationStatus,
        packageStarted: packageReservationGuests.packageStarted,
        packageCompleted: packageReservationGuests.packageCompleted,
        notes: packageReservationGuests.notes,
        createdAt: packageReservationGuests.createdAt,
        updatedAt: packageReservationGuests.updatedAt,
      })
      .from(packageReservationGuests)
      .leftJoin(guests, eq(packageReservationGuests.guestId, guests.id))
      .leftJoin(sql`customers`, eq(guests.customerId, sql`customers.id`))
      .where(eq(packageReservationGuests.reservationId, id));

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      `Viewed package reservation ${result.reservationNumber}`,
      { id: result.id, type: 'package-reservation' },
      { id: userId, type: 'user' },
      { packageReservationId: result.id, businessId: result.businessId },
    );

    return {
      id: result.id,
      businessId: result.businessId,
      reservationNumber: result.reservationNumber,
      referenceNumber: result.referenceNumber,
      packageId: result.packageId,
      package: {
        id: result.packageId,
        name: result.packageName,
        packageCode: result.packageCode,
        packageType: result.packageType,
        description: result.packageDescription,
      },
      startDate: result.startDate,
      endDate: result.endDate,
      totalNumberOfGuests: result.totalNumberOfGuests,
      numberOfAdults: result.numberOfAdults,
      numberOfChildren: result.numberOfChildren,
      status: result.status,
      reservationSource: result.reservationSource,
      paymentStatus: result.paymentStatus,
      subtotal: result.subtotal,
      total: result.total,
      depositPaid: result.depositPaid,
      balanceDue: result.balanceDue,
      discountType: result.discountType,
      discountValue: result.discountValue,
      discountAmount: result.discountAmount,
      taxType: result.taxType,
      notes: result.notes,
      cancellationReason: result.cancellationReason,
      cancellationDate: result.cancellationDate,
      confirmationSent: result.confirmationSent,
      confirmationSentAt: result.confirmationSentAt,
      reminderSent: result.reminderSent,
      reminderSentAt: result.reminderSentAt,
      guests: guestResults.map((guest) => ({
        id: guest.id,
        reservationId: guest.reservationId,
        guestId: guest.guestId,
        guest: {
          id: guest.guestId,
          firstName: guest.guestFirstName,
          lastName: guest.guestLastName,
          email: guest.guestEmail,
          phone: guest.guestPhone,
        },
        isPrimaryGuest: guest.isPrimaryGuest,
        primaryGuestId: guest.primaryGuestId,
        relationshipToPrimary: guest.relationshipToPrimary,
        participationStatus: guest.participationStatus,
        packageStarted: guest.packageStarted,
        packageCompleted: guest.packageCompleted,
        notes: guest.notes,
        createdAt: guest.createdAt,
        updatedAt: guest.updatedAt,
      })),
      createdBy: result.createdBy,
      updatedBy: result.updatedBy,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updatePackageReservationDto: UpdatePackageReservationDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if the reservation exists and belongs to the business
      const existingReservation = await this.db
        .select()
        .from(packageReservations)
        .where(
          and(
            eq(packageReservations.id, id),
            eq(packageReservations.businessId, businessId),
            isNull(packageReservations.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingReservation) {
        throw new NotFoundException(
          'Package reservation not found or does not belong to this business',
        );
      }

      // Check for duplicate reservation number if being updated
      if (
        updatePackageReservationDto.reservationNumber &&
        updatePackageReservationDto.reservationNumber !==
          existingReservation.reservationNumber
      ) {
        const duplicateReservation = await this.db
          .select()
          .from(packageReservations)
          .where(
            and(
              eq(packageReservations.businessId, businessId),
              ilike(
                packageReservations.reservationNumber,
                updatePackageReservationDto.reservationNumber,
              ),
              isNull(packageReservations.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (duplicateReservation) {
          throw new ConflictException(
            `A package reservation with the number '${updatePackageReservationDto.reservationNumber}' already exists for this business`,
          );
        }
      }

      // Check for duplicate reference number if being updated
      if (
        updatePackageReservationDto.referenceNumber &&
        updatePackageReservationDto.referenceNumber !==
          existingReservation.referenceNumber
      ) {
        const duplicateReferenceReservation = await this.db
          .select()
          .from(packageReservations)
          .where(
            and(
              eq(packageReservations.businessId, businessId),
              ilike(
                packageReservations.referenceNumber,
                updatePackageReservationDto.referenceNumber,
              ),
              isNull(packageReservations.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (duplicateReferenceReservation) {
          throw new ConflictException(
            `A package reservation with the reference number '${updatePackageReservationDto.referenceNumber}' already exists for this business`,
          );
        }
      }

      // Verify package exists if being updated
      if (
        updatePackageReservationDto.packageId &&
        updatePackageReservationDto.packageId !== existingReservation.packageId
      ) {
        const packageExists = await this.db
          .select()
          .from(packages)
          .where(
            and(
              eq(packages.id, updatePackageReservationDto.packageId),
              eq(packages.businessId, businessId),
              isNull(packages.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!packageExists) {
          throw new BadRequestException(
            'Package not found or does not belong to this business',
          );
        }
      }

      // Update the package reservation in a transaction
      const updatedReservation = await this.db.transaction(async (tx) => {
        const updateData: any = {
          updatedBy: userId,
          updatedAt: new Date(),
        };

        // Only update fields that are provided
        if (updatePackageReservationDto.reservationNumber !== undefined) {
          updateData.reservationNumber =
            updatePackageReservationDto.reservationNumber;
        }
        if (updatePackageReservationDto.referenceNumber !== undefined) {
          updateData.referenceNumber =
            updatePackageReservationDto.referenceNumber;
        }
        if (updatePackageReservationDto.packageId !== undefined) {
          updateData.packageId = updatePackageReservationDto.packageId;
        }
        if (updatePackageReservationDto.startDate !== undefined) {
          updateData.startDate = new Date(
            updatePackageReservationDto.startDate,
          );
        }
        if (updatePackageReservationDto.endDate !== undefined) {
          updateData.endDate = new Date(updatePackageReservationDto.endDate);
        }
        if (updatePackageReservationDto.totalNumberOfGuests !== undefined) {
          updateData.totalNumberOfGuests =
            updatePackageReservationDto.totalNumberOfGuests;
        }
        if (updatePackageReservationDto.numberOfAdults !== undefined) {
          updateData.numberOfAdults =
            updatePackageReservationDto.numberOfAdults;
        }
        if (updatePackageReservationDto.numberOfChildren !== undefined) {
          updateData.numberOfChildren =
            updatePackageReservationDto.numberOfChildren;
        }
        if (updatePackageReservationDto.status !== undefined) {
          updateData.status = updatePackageReservationDto.status;
        }
        if (updatePackageReservationDto.reservationSource !== undefined) {
          updateData.reservationSource =
            updatePackageReservationDto.reservationSource;
        }
        if (updatePackageReservationDto.paymentStatus !== undefined) {
          updateData.paymentStatus = updatePackageReservationDto.paymentStatus;
        }
        if (updatePackageReservationDto.subtotal !== undefined) {
          updateData.subtotal = updatePackageReservationDto.subtotal;
        }
        if (updatePackageReservationDto.total !== undefined) {
          updateData.total = updatePackageReservationDto.total;
        }
        if (updatePackageReservationDto.depositPaid !== undefined) {
          updateData.depositPaid = updatePackageReservationDto.depositPaid;
        }
        if (updatePackageReservationDto.balanceDue !== undefined) {
          updateData.balanceDue = updatePackageReservationDto.balanceDue;
        }
        if (updatePackageReservationDto.discountType !== undefined) {
          updateData.discountType = updatePackageReservationDto.discountType;
        }
        if (updatePackageReservationDto.discountValue !== undefined) {
          updateData.discountValue = updatePackageReservationDto.discountValue;
        }
        if (updatePackageReservationDto.discountAmount !== undefined) {
          updateData.discountAmount =
            updatePackageReservationDto.discountAmount;
        }
        if (updatePackageReservationDto.taxType !== undefined) {
          updateData.taxType = updatePackageReservationDto.taxType;
        }
        if (updatePackageReservationDto.notes !== undefined) {
          updateData.notes = updatePackageReservationDto.notes;
        }
        if (updatePackageReservationDto.cancellationReason !== undefined) {
          updateData.cancellationReason =
            updatePackageReservationDto.cancellationReason;
        }
        if (updatePackageReservationDto.cancellationDate !== undefined) {
          updateData.cancellationDate =
            updatePackageReservationDto.cancellationDate
              ? new Date(updatePackageReservationDto.cancellationDate)
              : null;
        }
        if (updatePackageReservationDto.confirmationSent !== undefined) {
          updateData.confirmationSent =
            updatePackageReservationDto.confirmationSent;
        }
        if (updatePackageReservationDto.confirmationSentAt !== undefined) {
          updateData.confirmationSentAt =
            updatePackageReservationDto.confirmationSentAt
              ? new Date(updatePackageReservationDto.confirmationSentAt)
              : null;
        }
        if (updatePackageReservationDto.reminderSent !== undefined) {
          updateData.reminderSent = updatePackageReservationDto.reminderSent;
        }
        if (updatePackageReservationDto.reminderSentAt !== undefined) {
          updateData.reminderSentAt = updatePackageReservationDto.reminderSentAt
            ? new Date(updatePackageReservationDto.reminderSentAt)
            : null;
        }

        const reservation = await tx
          .update(packageReservations)
          .set(updateData)
          .where(eq(packageReservations.id, id))
          .returning();

        return reservation[0];
      });

      // Handle guest associations if provided
      if (updatePackageReservationDto.guests) {
        // Remove existing guest associations
        await this.db
          .delete(packageReservationGuests)
          .where(eq(packageReservationGuests.reservationId, id));

        // Add new guest associations
        if (updatePackageReservationDto.guests.length > 0) {
          await this.manageReservationGuests(
            id,
            businessId,
            updatePackageReservationDto.guests,
            userId,
          );
        }
      }

      // Log the reservation update activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Package reservation "${existingReservation.reservationNumber}" was updated`,
        { id: updatedReservation.id.toString(), type: 'package-reservation' },
        { id: userId, type: 'user' },
        { packageReservationId: updatedReservation.id, businessId },
      );

      return {
        id: updatedReservation.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update package reservation: ${error.message}`,
      );
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updatePackageReservationDto: UpdatePackageReservationDto,
  ): Promise<{ id: string; message: string }> {
    const result = await this.update(
      userId,
      businessId,
      id,
      updatePackageReservationDto,
    );
    return {
      id: result.id,
      message: 'Package reservation updated successfully',
    };
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ id: string; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if the reservation exists and belongs to the business
      const existingReservation = await this.db
        .select()
        .from(packageReservations)
        .where(
          and(
            eq(packageReservations.id, id),
            eq(packageReservations.businessId, businessId),
            isNull(packageReservations.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingReservation) {
        throw new NotFoundException(
          'Package reservation not found or does not belong to this business',
        );
      }

      // Soft delete the reservation
      await this.db
        .update(packageReservations)
        .set({
          deletedAt: new Date(),
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(packageReservations.id, id));

      // Log the reservation deletion activity
      await this.activityLogService.log(
        ActivityLogName.DELETE,
        `Package reservation "${existingReservation.reservationNumber}" was deleted`,
        { id: existingReservation.id.toString(), type: 'package-reservation' },
        { id: userId, type: 'user' },
        { packageReservationId: existingReservation.id, businessId },
      );

      return {
        id: existingReservation.id,
        message: 'Package reservation deleted successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete package reservation: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createPackageReservationDtos: CreatePackageReservationDto[],
  ): Promise<{ ids: string[]; created: number; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (
        !createPackageReservationDtos ||
        createPackageReservationDtos.length === 0
      ) {
        throw new BadRequestException(
          'No package reservations provided for creation',
        );
      }

      // Check for duplicate reservation numbers within the batch
      const reservationNumbers = createPackageReservationDtos.map(
        (dto) => dto.reservationNumber,
      );
      const uniqueReservationNumbers = new Set(reservationNumbers);
      if (uniqueReservationNumbers.size !== reservationNumbers.length) {
        throw new ConflictException(
          'Duplicate reservation numbers found in the batch',
        );
      }

      // Check for existing reservation numbers in the database
      const existingReservations = await this.db
        .select({ reservationNumber: packageReservations.reservationNumber })
        .from(packageReservations)
        .where(
          and(
            eq(packageReservations.businessId, businessId),
            inArray(packageReservations.reservationNumber, reservationNumbers),
            isNull(packageReservations.deletedAt),
          ),
        );

      if (existingReservations.length > 0) {
        const existingNumbers = existingReservations.map(
          (r) => r.reservationNumber,
        );
        throw new ConflictException(
          `The following reservation numbers already exist: ${existingNumbers.join(', ')}`,
        );
      }

      // Verify all packages exist and belong to the business
      const packageIds = [
        ...new Set(createPackageReservationDtos.map((dto) => dto.packageId)),
      ];
      const existingPackages = await this.db
        .select({ id: packages.id })
        .from(packages)
        .where(
          and(
            inArray(packages.id, packageIds),
            eq(packages.businessId, businessId),
            isNull(packages.deletedAt),
          ),
        );

      if (existingPackages.length !== packageIds.length) {
        throw new BadRequestException(
          'One or more packages not found or do not belong to this business',
        );
      }

      // Create all reservations in a transaction
      const createdReservations = await this.db.transaction(async (tx) => {
        const reservations = [];

        for (const dto of createPackageReservationDtos) {
          const reservation = await tx
            .insert(packageReservations)
            .values({
              businessId,
              reservationNumber: dto.reservationNumber,
              referenceNumber: dto.referenceNumber,
              packageId: dto.packageId,
              startDate: new Date(dto.startDate),
              endDate: new Date(dto.endDate),
              totalNumberOfGuests: dto.totalNumberOfGuests,
              numberOfAdults: dto.numberOfAdults,
              numberOfChildren: dto.numberOfChildren ?? 0,
              status: dto.status ?? PackageReservationStatus.PENDING,
              reservationSource: dto.reservationSource,
              paymentStatus: dto.paymentStatus,
              subtotal: dto.subtotal ?? '0.00',
              total: dto.total ?? '0.00',
              depositPaid: dto.depositPaid,
              balanceDue: dto.balanceDue,
              discountType: dto.discountType,
              discountValue: dto.discountValue,
              discountAmount: dto.discountAmount,
              taxType: dto.taxType,
              notes: dto.notes,
              cancellationReason: dto.cancellationReason,
              cancellationDate: dto.cancellationDate
                ? new Date(dto.cancellationDate)
                : null,
              confirmationSent: dto.confirmationSent ?? false,
              confirmationSentAt: dto.confirmationSentAt
                ? new Date(dto.confirmationSentAt)
                : null,
              reminderSent: dto.reminderSent ?? false,
              reminderSentAt: dto.reminderSentAt
                ? new Date(dto.reminderSentAt)
                : null,
              createdBy: userId,
            })
            .returning();

          reservations.push(reservation[0]);
        }

        return reservations;
      });

      // Log the bulk creation activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Bulk created ${createdReservations.length} package reservations`,
        { id: businessId, type: 'business' },
        { id: userId, type: 'user' },
        { businessId, count: createdReservations.length },
      );

      return {
        ids: createdReservations.map((r) => r.id),
        created: createdReservations.length,
        message: `Successfully created ${createdReservations.length} package reservations`,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create package reservations: ${error.message}`,
      );
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    packageReservationIds: string[],
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
    failed?: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!packageReservationIds || packageReservationIds.length === 0) {
        throw new BadRequestException(
          'No package reservation IDs provided for deletion',
        );
      }

      // Find existing reservations that belong to the business
      const existingReservations = await this.db
        .select({
          id: packageReservations.id,
          reservationNumber: packageReservations.reservationNumber,
        })
        .from(packageReservations)
        .where(
          and(
            inArray(packageReservations.id, packageReservationIds),
            eq(packageReservations.businessId, businessId),
            isNull(packageReservations.deletedAt),
          ),
        );

      if (existingReservations.length === 0) {
        throw new NotFoundException(
          'No package reservations found for deletion',
        );
      }

      const existingIds = existingReservations.map((r) => r.id);
      const failedIds = packageReservationIds.filter(
        (id) => !existingIds.includes(id),
      );

      // Soft delete the reservations
      await this.db
        .update(packageReservations)
        .set({
          deletedAt: new Date(),
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(packageReservations.id, existingIds));

      // Log the bulk deletion activity
      await this.activityLogService.log(
        ActivityLogName.DELETE,
        `Bulk deleted ${existingReservations.length} package reservations`,
        { id: businessId, type: 'business' },
        { id: userId, type: 'user' },
        { businessId, count: existingReservations.length },
      );

      return {
        deleted: existingReservations.length,
        message: `Successfully deleted ${existingReservations.length} package reservations`,
        deletedIds: existingIds,
        failed: failedIds.length > 0 ? failedIds : undefined,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete package reservations: ${error.message}`,
      );
    }
  }

  async bulkUpdatePackageReservationStatus(
    userId: string,
    businessId: string | null,
    packageReservationIds: string[],
    status: PackageReservationStatus,
  ): Promise<{ updated: number; updatedIds: string[]; failed: string[] }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!packageReservationIds || packageReservationIds.length === 0) {
        throw new BadRequestException(
          'No package reservation IDs provided for status update',
        );
      }

      // Find existing reservations that belong to the business
      const existingReservations = await this.db
        .select({
          id: packageReservations.id,
          reservationNumber: packageReservations.reservationNumber,
        })
        .from(packageReservations)
        .where(
          and(
            inArray(packageReservations.id, packageReservationIds),
            eq(packageReservations.businessId, businessId),
            isNull(packageReservations.deletedAt),
          ),
        );

      if (existingReservations.length === 0) {
        throw new NotFoundException(
          'No package reservations found for status update',
        );
      }

      const existingIds = existingReservations.map((r) => r.id);
      const failedIds = packageReservationIds.filter(
        (id) => !existingIds.includes(id),
      );

      // Update the status of the reservations
      await this.db
        .update(packageReservations)
        .set({
          status,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(packageReservations.id, existingIds));

      // Log the bulk status update activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Bulk updated status to ${status} for ${existingReservations.length} package reservations`,
        { id: businessId, type: 'business' },
        { id: userId, type: 'user' },
        { businessId, count: existingReservations.length, status },
      );

      return {
        updated: existingReservations.length,
        updatedIds: existingIds,
        failed: failedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk update package reservation status: ${error.message}`,
      );
    }
  }

  async checkReservationNumberAvailability(
    _userId: string,
    businessId: string | null,
    reservationNumber: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingReservation = await this.db
      .select()
      .from(packageReservations)
      .where(
        and(
          eq(packageReservations.businessId, businessId),
          ilike(packageReservations.reservationNumber, reservationNumber),
          isNull(packageReservations.deletedAt),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingReservation };
  }

  async checkReferenceNumberAvailability(
    _userId: string,
    businessId: string | null,
    referenceNumber: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingReservation = await this.db
      .select()
      .from(packageReservations)
      .where(
        and(
          eq(packageReservations.businessId, businessId),
          ilike(packageReservations.referenceNumber, referenceNumber),
          isNull(packageReservations.deletedAt),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingReservation };
  }

  async checkPackageAvailability(
    _userId: string,
    businessId: string | null,
    startDate: string,
    endDate: string,
    adults: number,
    children: number = 0,
    packageIds?: string[],
  ): Promise<any> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const start = new Date(startDate);
    const end = new Date(endDate);
    const days = Math.ceil(
      (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24),
    );

    // Build package query conditions
    const packageConditions = [
      eq(packages.businessId, businessId),
      isNull(packages.deletedAt),
    ];

    if (packageIds && packageIds.length > 0) {
      packageConditions.push(inArray(packages.id, packageIds));
    }

    // Get available packages
    const availablePackages = await this.db
      .select({
        id: packages.id,
        name: packages.name,
        packageCode: packages.packageCode,
        type: packages.type,
        description: packages.description,
        basePrice: packages.basePrice,
        salePrice: packages.salePrice,
        shortDescription: packages.shortDescription,
      })
      .from(packages)
      .where(and(...packageConditions));

    // For now, assume all packages can accommodate the requested guests
    // In a real implementation, you would have capacity fields or check against existing reservations
    const filteredPackages = availablePackages;

    // Calculate pricing for each package
    const packagesWithPricing = filteredPackages.map((pkg) => {
      const price = parseFloat(pkg.salePrice) || parseFloat(pkg.basePrice);
      const totalCost = (price * (adults + children * 0.5) * days).toFixed(2);

      return {
        id: pkg.id,
        name: pkg.name,
        packageCode: pkg.packageCode,
        packageType: pkg.type,
        maxAdults: 10, // Default capacity - should be configurable
        maxChildren: 5, // Default capacity - should be configurable
        basePrice: pkg.basePrice,
        totalCost,
        days,
        description: pkg.description || pkg.shortDescription || '',
        inclusions: [], // Would come from a related table in real implementation
        images: [], // Would come from media relations in real implementation
      };
    });

    return {
      startDate,
      endDate,
      days,
      adults,
      children,
      availablePackages: packagesWithPricing,
      totalAvailable: packagesWithPricing.length,
    };
  }

  async calculatePackagePricing(
    _userId: string,
    businessId: string | null,
    packageId: string,
    startDate: string,
    endDate: string,
    adults: number,
    children: number = 0,
    discountType?: string,
    discountValue?: string,
    taxType?: string,
  ): Promise<any> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get package details
    const packageDetails = await this.db
      .select()
      .from(packages)
      .where(
        and(
          eq(packages.id, packageId),
          eq(packages.businessId, businessId),
          isNull(packages.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!packageDetails) {
      throw new NotFoundException('Package not found');
    }

    const start = new Date(startDate);
    const end = new Date(endDate);
    const days = Math.ceil(
      (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24),
    );

    // Calculate base pricing
    const basePricePerPersonPerDay = parseFloat(packageDetails.basePrice);
    const adultSubtotal = basePricePerPersonPerDay * adults * days;
    const childrenSubtotal = basePricePerPersonPerDay * children * 0.5 * days; // Children at 50% rate
    const packageSubtotal = adultSubtotal + childrenSubtotal;

    // Calculate discount
    let discountAmount = 0;
    if (discountType && discountValue) {
      if (discountType === 'PERCENTAGE') {
        discountAmount = (packageSubtotal * parseFloat(discountValue)) / 100;
      } else if (discountType === 'FIXED') {
        discountAmount = parseFloat(discountValue);
      }
    }

    // Calculate tax (assuming 10% tax rate for demo)
    const taxRate = 10;
    const taxableAmount = packageSubtotal - discountAmount;
    const taxAmount = (taxableAmount * taxRate) / 100;

    const total =
      packageSubtotal -
      discountAmount +
      (taxType === 'EXCLUSIVE' ? taxAmount : 0);

    // Generate daily rates
    const dailyRates: Record<string, string> = {};
    const dailyRate = (packageSubtotal / days).toFixed(2);
    for (let i = 0; i < days; i++) {
      const date = new Date(start);
      date.setDate(date.getDate() + i);
      dailyRates[date.toISOString().split('T')[0]] = dailyRate;
    }

    const packageBreakdown = {
      packageId,
      packageName: packageDetails.name,
      basePricePerPersonPerDay: basePricePerPersonPerDay.toFixed(2),
      days,
      adults,
      children,
      adultSubtotal: adultSubtotal.toFixed(2),
      childrenSubtotal: childrenSubtotal.toFixed(2),
      packageSubtotal: packageSubtotal.toFixed(2),
      packageDiscountAmount: discountAmount.toFixed(2),
      packageTaxAmount: taxAmount.toFixed(2),
      packageTotal: total.toFixed(2),
      dailyRates,
    };

    return {
      startDate,
      endDate,
      days,
      totalAdults: adults,
      totalChildren: children,
      packageBreakdown,
      subtotal: packageSubtotal.toFixed(2),
      totalDiscountAmount: discountAmount.toFixed(2),
      totalTaxAmount: taxAmount.toFixed(2),
      total: total.toFixed(2),
      appliedDiscount:
        discountType && discountValue
          ? {
              type: discountType,
              value: discountValue,
            }
          : undefined,
      taxBreakdown: {
        taxType: taxType || 'INCLUSIVE',
        taxRate: taxRate.toFixed(2),
        taxableAmount: taxableAmount.toFixed(2),
      },
    };
  }

  async confirmPackageReservation(
    userId: string,
    businessId: string | null,
    id: string,
    confirmationData: any,
  ): Promise<any> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if the reservation exists and belongs to the business
    const existingReservation = await this.db
      .select()
      .from(packageReservations)
      .where(
        and(
          eq(packageReservations.id, id),
          eq(packageReservations.businessId, businessId),
          isNull(packageReservations.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingReservation) {
      throw new NotFoundException(
        'Package reservation not found or does not belong to this business',
      );
    }

    // Update reservation status to confirmed
    const confirmationDate = confirmationData.confirmationDate
      ? new Date(confirmationData.confirmationDate)
      : new Date();

    const updatedReservation = await this.db
      .update(packageReservations)
      .set({
        status: PackageReservationStatus.CONFIRMED,
        paymentStatus:
          confirmationData.paymentStatus || existingReservation.paymentStatus,
        depositPaid:
          confirmationData.depositPaid || existingReservation.depositPaid,
        balanceDue:
          confirmationData.balanceDue || existingReservation.balanceDue,
        confirmationSent: confirmationData.sendConfirmation !== false,
        confirmationSentAt:
          confirmationData.sendConfirmation !== false ? new Date() : null,
        notes: confirmationData.confirmationNotes
          ? existingReservation.notes
            ? `${existingReservation.notes}\n\nConfirmation: ${confirmationData.confirmationNotes}`
            : confirmationData.confirmationNotes
          : existingReservation.notes,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(packageReservations.id, id))
      .returning();

    // Log the confirmation activity
    await this.activityLogService.log(
      ActivityLogName.UPDATE,
      `Package reservation "${existingReservation.reservationNumber}" was confirmed`,
      { id: existingReservation.id.toString(), type: 'package-reservation' },
      { id: userId, type: 'user' },
      { packageReservationId: existingReservation.id, businessId },
    );

    return {
      id: updatedReservation[0].id,
      reservationNumber: existingReservation.reservationNumber,
      status: PackageReservationStatus.CONFIRMED,
      paymentStatus: updatedReservation[0].paymentStatus,
      confirmationDate,
      confirmationSent: updatedReservation[0].confirmationSent,
      confirmationSentAt: updatedReservation[0].confirmationSentAt,
      message: 'Package reservation confirmed successfully',
      guestsNotified: confirmationData.sendConfirmation !== false ? 1 : 0, // Would be actual count in real implementation
    };
  }

  async cancelPackageReservation(
    userId: string,
    businessId: string | null,
    id: string,
    cancellationData: any,
  ): Promise<any> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if the reservation exists and belongs to the business
    const existingReservation = await this.db
      .select()
      .from(packageReservations)
      .where(
        and(
          eq(packageReservations.id, id),
          eq(packageReservations.businessId, businessId),
          isNull(packageReservations.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingReservation) {
      throw new NotFoundException(
        'Package reservation not found or does not belong to this business',
      );
    }

    // Update reservation status to cancelled
    const cancellationDate = cancellationData.cancellationDate
      ? new Date(cancellationData.cancellationDate)
      : new Date();

    const updatedReservation = await this.db
      .update(packageReservations)
      .set({
        status: PackageReservationStatus.CANCELLED,
        cancellationReason: cancellationData.cancellationReason,
        cancellationDate,
        notes: cancellationData.cancellationNotes
          ? existingReservation.notes
            ? `${existingReservation.notes}\n\nCancellation: ${cancellationData.cancellationNotes}`
            : cancellationData.cancellationNotes
          : existingReservation.notes,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(packageReservations.id, id))
      .returning();

    // Log the cancellation activity
    await this.activityLogService.log(
      ActivityLogName.UPDATE,
      `Package reservation "${existingReservation.reservationNumber}" was cancelled: ${cancellationData.cancellationReason}`,
      { id: existingReservation.id.toString(), type: 'package-reservation' },
      { id: userId, type: 'user' },
      { packageReservationId: existingReservation.id, businessId },
    );

    return {
      id: updatedReservation[0].id,
      reservationNumber: existingReservation.reservationNumber,
      status: PackageReservationStatus.CANCELLED,
      cancellationReason: cancellationData.cancellationReason,
      cancellationDate,
      notificationSent: cancellationData.sendNotification !== false,
      refundProcessed: cancellationData.processRefund === true,
      refundAmount:
        cancellationData.processRefund === true
          ? cancellationData.refundAmount
          : undefined,
      message: 'Package reservation cancelled successfully',
      guestsNotified: cancellationData.sendNotification !== false ? 1 : 0, // Would be actual count in real implementation
    };
  }

  async addGuestToReservation(
    userId: string,
    businessId: string | null,
    reservationId: string,
    guestData: any,
  ): Promise<any> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if the reservation exists and belongs to the business
    const existingReservation = await this.db
      .select()
      .from(packageReservations)
      .where(
        and(
          eq(packageReservations.id, reservationId),
          eq(packageReservations.businessId, businessId),
          isNull(packageReservations.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingReservation) {
      throw new NotFoundException(
        'Package reservation not found or does not belong to this business',
      );
    }

    // Check if guest is already associated with this reservation
    const existingGuestAssociation = await this.db
      .select()
      .from(packageReservationGuests)
      .where(
        and(
          eq(packageReservationGuests.reservationId, reservationId),
          eq(packageReservationGuests.guestId, guestData.guestId),
        ),
      )
      .then((results) => results[0]);

    if (existingGuestAssociation) {
      throw new ConflictException(
        'Guest is already associated with this reservation',
      );
    }

    // Verify guest exists and get customer info
    const guest = await this.db
      .select({
        id: guests.id,
        guestNumber: guests.guestNumber,
        customerId: guests.customerId,
        customerDisplayName: sql<string>`customers.display_name`,
        customerEmail: sql<string>`customers.email`,
        customerPhone: sql<string>`customers.phone`,
      })
      .from(guests)
      .leftJoin(sql`customers`, eq(guests.customerId, sql`customers.id`))
      .where(eq(guests.id, guestData.guestId))
      .then((results) => results[0]);

    if (!guest) {
      throw new NotFoundException('Guest not found');
    }

    // Add guest to reservation
    const newGuestAssociation = await this.db
      .insert(packageReservationGuests)
      .values({
        reservationId,
        guestId: guestData.guestId,
        isPrimaryGuest: guestData.isPrimaryGuest ?? false,
        primaryGuestId: guestData.primaryGuestId,
        relationshipToPrimary: guestData.relationshipToPrimary,
        participationStatus: guestData.participationStatus ?? 'CONFIRMED',
        packageStarted: false,
        packageCompleted: false,
        notes: guestData.notes,
        createdBy: userId,
      })
      .returning();

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.UPDATE,
      `Guest ${guest.customerDisplayName} added to package reservation "${existingReservation.reservationNumber}"`,
      { id: existingReservation.id.toString(), type: 'package-reservation' },
      { id: userId, type: 'user' },
      {
        packageReservationId: existingReservation.id,
        businessId,
        guestId: guest.id,
      },
    );

    return {
      id: newGuestAssociation[0].id,
      guestId: guest.id,
      guest: {
        id: guest.id,
        firstName: guest.customerDisplayName?.split(' ')[0] || '',
        lastName:
          guest.customerDisplayName?.split(' ').slice(1).join(' ') || '',
        email: guest.customerEmail,
        phone: guest.customerPhone,
      },
      isPrimaryGuest: newGuestAssociation[0].isPrimaryGuest,
      participationStatus: newGuestAssociation[0].participationStatus,
      message: 'Guest added to package reservation successfully',
    };
  }

  async removeGuestFromReservation(
    userId: string,
    businessId: string | null,
    reservationId: string,
    guestId: string,
    removalData: any,
  ): Promise<any> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if the reservation exists and belongs to the business
    const existingReservation = await this.db
      .select()
      .from(packageReservations)
      .where(
        and(
          eq(packageReservations.id, reservationId),
          eq(packageReservations.businessId, businessId),
          isNull(packageReservations.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingReservation) {
      throw new NotFoundException(
        'Package reservation not found or does not belong to this business',
      );
    }

    // Check if guest is associated with this reservation
    const guestAssociation = await this.db
      .select({
        guestAssociationId: packageReservationGuests.id,
        isPrimaryGuest: packageReservationGuests.isPrimaryGuest,
        guestId: guests.id,
        guestNumber: guests.guestNumber,
        customerId: guests.customerId,
        customerDisplayName: sql<string>`customers.display_name`,
        customerEmail: sql<string>`customers.email`,
        customerPhone: sql<string>`customers.phone`,
      })
      .from(packageReservationGuests)
      .leftJoin(guests, eq(packageReservationGuests.guestId, guests.id))
      .leftJoin(sql`customers`, eq(guests.customerId, sql`customers.id`))
      .where(
        and(
          eq(packageReservationGuests.reservationId, reservationId),
          eq(packageReservationGuests.guestId, guestId),
        ),
      )
      .then((results) => results[0]);

    if (!guestAssociation) {
      throw new NotFoundException(
        'Guest is not associated with this reservation',
      );
    }

    // Check if this is the primary guest and if there are other guests
    if (guestAssociation.isPrimaryGuest) {
      const otherGuests = await this.db
        .select()
        .from(packageReservationGuests)
        .where(
          and(
            eq(packageReservationGuests.reservationId, reservationId),
            sql`${packageReservationGuests.guestId} != ${guestId}`,
          ),
        );

      if (otherGuests.length > 0) {
        throw new BadRequestException(
          'Cannot remove primary guest when other guests are present. Please designate a new primary guest first.',
        );
      }
    }

    // Remove guest from reservation
    await this.db
      .delete(packageReservationGuests)
      .where(
        and(
          eq(packageReservationGuests.reservationId, reservationId),
          eq(packageReservationGuests.guestId, guestId),
        ),
      );

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.UPDATE,
      `Guest ${guestAssociation.customerDisplayName} removed from package reservation "${existingReservation.reservationNumber}": ${removalData.removalReason}`,
      { id: existingReservation.id.toString(), type: 'package-reservation' },
      { id: userId, type: 'user' },
      { packageReservationId: existingReservation.id, businessId, guestId },
    );

    return {
      id: guestAssociation.guestAssociationId,
      guestId,
      guest: {
        id: guestId,
        firstName: guestAssociation.customerDisplayName?.split(' ')[0] || '',
        lastName:
          guestAssociation.customerDisplayName?.split(' ').slice(1).join(' ') ||
          '',
        email: guestAssociation.customerEmail,
        phone: guestAssociation.customerPhone,
      },
      removalReason: removalData.removalReason,
      message: 'Guest removed from package reservation successfully',
    };
  }

  async sendPackageConfirmation(
    userId: string,
    businessId: string | null,
    reservationId: string,
    confirmationData: any,
  ): Promise<any> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if the reservation exists and belongs to the business
    const existingReservation = await this.db
      .select()
      .from(packageReservations)
      .where(
        and(
          eq(packageReservations.id, reservationId),
          eq(packageReservations.businessId, businessId),
          isNull(packageReservations.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingReservation) {
      throw new NotFoundException(
        'Package reservation not found or does not belong to this business',
      );
    }

    // Update confirmation sent status
    await this.db
      .update(packageReservations)
      .set({
        confirmationSent: true,
        confirmationSentAt: new Date(),
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(packageReservations.id, reservationId));

    // In a real implementation, this would integrate with email/SMS services
    const communicationTypes = confirmationData.communicationTypes || ['EMAIL'];
    const results = communicationTypes.map((type: string) => ({
      type,
      success: true,
      recipient: '<EMAIL>', // Would be actual guest contact info
      messageId: `msg_${Date.now()}`,
    }));

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.UPDATE,
      `Package confirmation sent for reservation "${existingReservation.reservationNumber}"`,
      { id: existingReservation.id.toString(), type: 'package-reservation' },
      { id: userId, type: 'user' },
      { packageReservationId: existingReservation.id, businessId },
    );

    return {
      reservationId,
      reservationNumber: existingReservation.reservationNumber,
      results,
      successCount: results.filter((r: any) => r.success).length,
      failureCount: results.filter((r: any) => !r.success).length,
      guestsContacted: 1, // Would be actual count in real implementation
      message: 'Package confirmation sent successfully',
    };
  }

  async sendPackageReminder(
    userId: string,
    businessId: string | null,
    reservationId: string,
    reminderData: any,
  ): Promise<any> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if the reservation exists and belongs to the business
    const existingReservation = await this.db
      .select()
      .from(packageReservations)
      .where(
        and(
          eq(packageReservations.id, reservationId),
          eq(packageReservations.businessId, businessId),
          isNull(packageReservations.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingReservation) {
      throw new NotFoundException(
        'Package reservation not found or does not belong to this business',
      );
    }

    // Update reminder sent status
    await this.db
      .update(packageReservations)
      .set({
        reminderSent: true,
        reminderSentAt: new Date(),
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(packageReservations.id, reservationId));

    // In a real implementation, this would integrate with email/SMS services
    const communicationTypes = reminderData.communicationTypes || ['EMAIL'];
    const results = communicationTypes.map((type: string) => ({
      type,
      success: true,
      recipient: '<EMAIL>', // Would be actual guest contact info
      messageId: `msg_${Date.now()}`,
    }));

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.UPDATE,
      `Package reminder sent for reservation "${existingReservation.reservationNumber}"`,
      { id: existingReservation.id.toString(), type: 'package-reservation' },
      { id: userId, type: 'user' },
      { packageReservationId: existingReservation.id, businessId },
    );

    return {
      reservationId,
      reservationNumber: existingReservation.reservationNumber,
      results,
      successCount: results.filter((r: any) => r.success).length,
      failureCount: results.filter((r: any) => !r.success).length,
      guestsContacted: 1, // Would be actual count in real implementation
      message: 'Package reminder sent successfully',
    };
  }
}
