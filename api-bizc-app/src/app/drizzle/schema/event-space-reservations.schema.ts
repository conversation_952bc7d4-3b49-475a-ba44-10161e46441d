import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  timestamp,
  integer,
  decimal,
  jsonb,
  boolean,
} from 'drizzle-orm/pg-core';
import { isNull } from 'drizzle-orm';
import { business } from './business.schema';
import { eventSpaces } from './event-spaces.schema';
import { customers } from './customers.schema';
import { guests } from './guests.schema';
import { accounts } from './accounts.schema';
import { taxes } from './taxes.schema';
import { assets } from './assets.schema';
import { auditFields } from './common-fields.schema';

// Event Space Reservation Status Enum
export const eventReservationStatusEnum = pgEnum('event_reservation_status', [
  'INQUIRY',
  'TENTATIVE',
  'CONFIRMED',
  'SETUP_IN_PROGRESS',
  'EVENT_IN_PROGRESS',
  'BREAKDOWN_IN_PROGRESS',
  'COMPLETED',
  'CANCELLED',
  'NO_SHOW',
  'BLOCKED',
]);

// Event Type Enum
export const eventTypeEnum = pgEnum('event_type', [
  'WEDDING',
  'CORPORATE_EVENT',
  'CONFERENCE',
  'MEETING',
  'BIRTHDAY_PARTY',
  'ANNIVERSARY',
  'GRADUATION',
  'BABY_SHOWER',
  'BRIDAL_SHOWER',
  'SEMINAR',
  'WORKSHOP',
  'EXHIBITION',
  'PRODUCT_LAUNCH',
  'NETWORKING_EVENT',
  'CHARITY_EVENT',
  'CULTURAL_EVENT',
  'RELIGIOUS_EVENT',
  'SPORTS_EVENT',
  'CONCERT',
  'THEATER',
  'OTHER',
]);

// Payment Status Enum
export const paymentStatusEnum = pgEnum('payment_status', [
  'PENDING',
  'PARTIAL',
  'PAID',
  'REFUNDED',
  'CANCELLED',
]);

// Setup Style Enum (from shared types)
export const setupStyleEnum = pgEnum('setup_style', [
  'THEATER',
  'CLASSROOM',
  'BANQUET',
  'COCKTAIL',
  'BOARDROOM',
  'U_SHAPE',
  'HOLLOW_SQUARE',
  'CABARET',
  'CONFERENCE',
  'RECEPTION',
  'CUSTOM',
]);

// Event Requirements Interface
export interface EventRequirements {
  audioVisual?: {
    microphones?: number;
    speakers?: boolean;
    projector?: boolean;
    screen?: boolean;
    lighting?: string;
    recording?: boolean;
    livestream?: boolean;
  };
  catering?: {
    required?: boolean;
    type?: string; // Breakfast, Lunch, Dinner, Cocktail, etc.
    guestCount?: number;
    dietaryRestrictions?: string[];
    menuPreferences?: string;
    servicingStyle?: string; // Buffet, Plated, Family Style
  };
  furniture?: {
    tables?: number;
    chairs?: number;
    podium?: boolean;
    dance_floor?: boolean;
    bar?: boolean;
    stage?: boolean;
  };
  decorations?: {
    flowers?: boolean;
    balloons?: boolean;
    lighting?: string;
    theme?: string;
    colors?: string[];
  };
  technology?: {
    wifi?: boolean;
    powerOutlets?: number;
    screens?: number;
    conferenceEquipment?: boolean;
  };
  accessibility?: {
    wheelchairAccess?: boolean;
    signLanguage?: boolean;
    assistiveListening?: boolean;
  };
  security?: {
    required?: boolean;
    guestList?: boolean;
    idVerification?: boolean;
  };
  other?: string[];
  [key: string]: any;
}

// Billing Information Interface
export interface EventBillingInformation {
  spaceRental: number;
  setupFee?: number;
  cleanupFee?: number;
  equipmentCharges?: number;
  cateringCharges?: number;
  serviceFees?: number;
  overtime?: number;
  damages?: number;
  subtotal: number;
  taxAmount: number;
  discountAmount?: number;
  totalAmount: number;
  depositAmount?: number;
  balanceAmount?: number;
  currency: string;
  taxBreakdown?: {
    taxName: string;
    taxRate: number;
    taxAmount: number;
  }[];
  additionalCharges?: {
    name: string;
    amount: number;
    description?: string;
  }[];
  [key: string]: any;
}

// Event Space Reservations table
export const eventSpaceReservations = pgTable(
  'event_space_reservations',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Reservation identification
    reservationNumber: text('reservation_number').notNull(),
    referenceNumber: text('reference_number'), // External booking reference

    // Space and customer references
    eventSpaceId: uuid('event_space_id')
      .notNull()
      .references(() => eventSpaces.id),
    customerId: uuid('customer_id').references(() => customers.id),

    // Primary contact (organizer) reference
    primaryContactId: uuid('primary_contact_id')
      .notNull()
      .references(() => guests.id),

    // Event details
    eventName: text('event_name').notNull(),
    eventType: eventTypeEnum('event_type').notNull(),
    eventDescription: text('event_description'),

    // Date and time scheduling
    eventStartTime: timestamp('event_start_time').notNull(),
    eventEndTime: timestamp('event_end_time').notNull(),
    setupStartTime: timestamp('setup_start_time').notNull(),
    breakdownEndTime: timestamp('breakdown_end_time').notNull(),

    // Actual times (for tracking)
    actualSetupStartTime: timestamp('actual_setup_start_time'),
    actualEventStartTime: timestamp('actual_event_start_time'),
    actualEventEndTime: timestamp('actual_event_end_time'),
    actualBreakdownEndTime: timestamp('actual_breakdown_end_time'),

    // Occupancy and setup
    expectedGuestCount: integer('expected_guest_count').notNull(),
    confirmedGuestCount: integer('confirmed_guest_count').default(0),
    setupStyle: setupStyleEnum('setup_style').notNull(),
    customSetupDescription: text('custom_setup_description'),

    // Guest management preferences
    requiresIndividualGuestTracking: boolean(
      'requires_individual_guest_tracking',
    )
      .default(false)
      .notNull(), // For weddings/large events, often false. For corporate/small events, might be true
    guestListDeadline: timestamp('guest_list_deadline'), // When final guest count/list is due
    allowWalkIns: boolean('allow_walk_ins').default(false).notNull(), // Whether to allow additional guests on event day

    // Event requirements and equipment
    requirements: jsonb('requirements')
      .$type<EventRequirements>()
      .default({})
      .notNull(),

    // Reservation status and type
    status: eventReservationStatusEnum('status').default('INQUIRY').notNull(),
    reservationSource: text('reservation_source'), // Online, Phone, Walk-in, etc.
    isRecurring: boolean('is_recurring').default(false).notNull(),
    parentReservationId: uuid('parent_reservation_id'), // For recurring events

    // Pricing information
    hourlyRate: decimal('hourly_rate', { precision: 12, scale: 2 }).notNull(),
    totalHours: decimal('total_hours', { precision: 6, scale: 2 }).notNull(),
    totalSpaceCost: decimal('total_space_cost', {
      precision: 12,
      scale: 2,
    }).notNull(),

    // Billing information
    billingInformation: jsonb('billing_information')
      .$type<EventBillingInformation>()
      .notNull(),

    // Payment information
    paymentStatus: paymentStatusEnum('payment_status')
      .default('PENDING')
      .notNull(),
    depositRequired: decimal('deposit_required', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    depositPaid: decimal('deposit_paid', { precision: 12, scale: 2 }).default(
      '0.00',
    ),
    balanceDue: decimal('balance_due', { precision: 12, scale: 2 }).default(
      '0.00',
    ),

    // Account references for financial tracking
    incomeAccountId: uuid('income_account_id').references(() => accounts.id),
    expenseAccountId: uuid('expense_account_id').references(() => accounts.id),

    // Tax information
    taxType: pgEnum('tax_type', ['INCLUSIVE', 'EXCLUSIVE', 'OUT_OF_SCOPE'])(
      'tax_type',
    )
      .default('INCLUSIVE')
      .notNull(),
    defaultTaxRateId: uuid('default_tax_rate_id').references(() => taxes.id),

    // Event metadata
    notes: text('notes'),
    internalNotes: text('internal_notes'), // Staff-only notes
    specialRequests: text('special_requests'),
    cancellationReason: text('cancellation_reason'),
    cancellationDate: timestamp('cancellation_date'),

    // Contract and legal
    contractSigned: boolean('contract_signed').default(false).notNull(),
    contractSignedDate: timestamp('contract_signed_date'),
    insuranceRequired: boolean('insurance_required').default(false).notNull(),
    insuranceProvided: boolean('insurance_provided').default(false).notNull(),

    // Communication tracking
    confirmationSent: boolean('confirmation_sent').default(false).notNull(),
    confirmationSentAt: timestamp('confirmation_sent_at'),
    reminderSent: boolean('reminder_sent').default(false).notNull(),
    reminderSentAt: timestamp('reminder_sent_at'),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    // Primary indexes
    businessIdIndex: index('event_reservations_business_id_index').on(
      t.businessId,
    ),
    reservationNumberIndex: index('event_reservations_number_index').on(
      t.reservationNumber,
    ),
    referenceNumberIndex: index('event_reservations_reference_index').on(
      t.referenceNumber,
    ),

    // Foreign key indexes
    eventSpaceIdIndex: index('event_reservations_space_id_index').on(
      t.eventSpaceId,
    ),
    customerIdIndex: index('event_reservations_customer_id_index').on(
      t.customerId,
    ),
    primaryContactIdIndex: index('event_reservations_primary_contact_index').on(
      t.primaryContactId,
    ),

    // Event details indexes
    eventNameIndex: index('event_reservations_event_name_index').on(
      t.eventName,
    ),
    eventTypeIndex: index('event_reservations_event_type_index').on(
      t.eventType,
    ),

    // Date and time indexes for availability queries
    eventStartTimeIndex: index('event_reservations_event_start_time_index').on(
      t.eventStartTime,
    ),
    eventEndTimeIndex: index('event_reservations_event_end_time_index').on(
      t.eventEndTime,
    ),
    setupStartTimeIndex: index('event_reservations_setup_start_time_index').on(
      t.setupStartTime,
    ),
    breakdownEndTimeIndex: index(
      'event_reservations_breakdown_end_time_index',
    ).on(t.breakdownEndTime),

    // Status and type indexes
    statusIndex: index('event_reservations_status_index').on(t.status),
    paymentStatusIndex: index('event_reservations_payment_status_index').on(
      t.paymentStatus,
    ),
    reservationSourceIndex: index('event_reservations_source_index').on(
      t.reservationSource,
    ),
    setupStyleIndex: index('event_reservations_setup_style_index').on(
      t.setupStyle,
    ),

    // Recurring and grouping indexes
    isRecurringIndex: index('event_reservations_is_recurring_index').on(
      t.isRecurring,
    ),
    parentReservationIdIndex: index(
      'event_reservations_parent_reservation_index',
    ).on(t.parentReservationId),

    // Contract and legal indexes
    contractSignedIndex: index('event_reservations_contract_signed_index').on(
      t.contractSigned,
    ),
    insuranceRequiredIndex: index(
      'event_reservations_insurance_required_index',
    ).on(t.insuranceRequired),

    // Guest management indexes
    requiresIndividualGuestTrackingIndex: index(
      'event_reservations_requires_individual_guest_tracking_index',
    ).on(t.requiresIndividualGuestTracking),
    guestListDeadlineIndex: index(
      'event_reservations_guest_list_deadline_index',
    ).on(t.guestListDeadline),
    allowWalkInsIndex: index('event_reservations_allow_walk_ins_index').on(
      t.allowWalkIns,
    ),

    // Communication indexes
    confirmationSentIndex: index(
      'event_reservations_confirmation_sent_index',
    ).on(t.confirmationSent),

    // Account references indexes
    incomeAccountIdIndex: index('event_reservations_income_account_index').on(
      t.incomeAccountId,
    ),
    expenseAccountIdIndex: index('event_reservations_expense_account_index').on(
      t.expenseAccountId,
    ),
    defaultTaxRateIdIndex: index('event_reservations_tax_rate_index').on(
      t.defaultTaxRateId,
    ),

    // Composite indexes for common queries
    businessStatusIndex: index('event_reservations_business_status_index').on(
      t.businessId,
      t.status,
    ),
    businessSpaceStatusIndex: index(
      'event_reservations_business_space_status_index',
    ).on(t.businessId, t.eventSpaceId, t.status),
    businessEventDateIndex: index(
      'event_reservations_business_event_date_index',
    ).on(t.businessId, t.eventStartTime, t.eventEndTime),
    spaceAvailabilityIndex: index('event_reservations_space_availability').on(
      t.eventSpaceId,
      t.setupStartTime,
      t.breakdownEndTime,
      t.status,
    ),
    customerReservationsIndex: index('event_reservations_customer_index').on(
      t.customerId,
      t.status,
      t.eventStartTime,
    ),
    paymentDueIndex: index('event_reservations_payment_due_index').on(
      t.businessId,
      t.paymentStatus,
      t.eventStartTime,
    ),
    recurringSeriesIndex: index('event_reservations_recurring_series').on(
      t.parentReservationId,
      t.eventStartTime,
    ),

    // Unique constraints with soft deletion support
    uniqueBusinessReservationNumber: uniqueIndex(
      'event_reservations_business_number_unique',
    )
      .on(t.businessId, t.reservationNumber)
      .where(isNull(t.deletedAt)),
    uniqueBusinessReferenceNumber: uniqueIndex(
      'event_reservations_business_reference_unique',
    )
      .on(t.businessId, t.referenceNumber)
      .where(isNull(t.deletedAt)),

    // Availability check constraint (overlapping reservations for same space)
    // Note: This should be handled at application level or with more complex constraints
    uniqueSpaceTimeSlot: index('event_reservations_space_time_overlap_check')
      .on(t.eventSpaceId, t.setupStartTime, t.breakdownEndTime, t.status)
      .where(isNull(t.deletedAt)),
  }),
);

// Junction table for event attendees (OPTIONAL - only used when requiresIndividualGuestTracking = true)
// For weddings/large events, this table may be empty - just use expectedGuestCount from main reservation
// For corporate events/small gatherings, this table provides detailed attendee management
export const eventReservationAttendees = pgTable(
  'event_reservation_attendees',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    reservationId: uuid('reservation_id')
      .notNull()
      .references(() => eventSpaceReservations.id, {
        onDelete: 'cascade',
      }),
    guestId: uuid('guest_id')
      .notNull()
      .references(() => guests.id, {
        onDelete: 'cascade',
      }),

    // Attendee role in this specific event
    attendeeRole: text('attendee_role'), // Organizer, Speaker, VIP, Guest, Vendor, etc.
    isVip: boolean('is_vip').default(false).notNull(),
    isConfirmed: boolean('is_confirmed').default(false).notNull(),

    // RSVP tracking
    rsvpStatus: text('rsvp_status').default('PENDING').notNull(), // PENDING, CONFIRMED, DECLINED, NO_RESPONSE
    rsvpDate: timestamp('rsvp_date'),
    rsvpNotes: text('rsvp_notes'),

    // Check-in tracking
    checkedIn: boolean('checked_in').default(false).notNull(),
    checkInTime: timestamp('check_in_time'),
    checkedOut: boolean('checked_out').default(false).notNull(),
    checkOutTime: timestamp('check_out_time'),

    // Event-specific attendee information
    dietaryRestrictions: text('dietary_restrictions').array().default([]),
    accessibilityNeeds: text('accessibility_needs').array().default([]),
    specialRequests: text('special_requests'),
    tableAssignment: text('table_assignment'),
    seatNumber: text('seat_number'),

    // Communication preferences for this event
    receiveUpdates: boolean('receive_updates').default(true).notNull(),
    receiveReminders: boolean('receive_reminders').default(true).notNull(),

    // Plus-one information
    hasPlusOne: boolean('has_plus_one').default(false).notNull(),
    plusOneName: text('plus_one_name'),
    plusOneCheckedIn: boolean('plus_one_checked_in').default(false).notNull(),

    // Notes specific to this attendee for this event
    notes: text('notes'),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    // Primary indexes
    reservationIdIndex: index(
      'event_reservation_attendees_reservation_id_index',
    ).on(t.reservationId),
    guestIdIndex: index('event_reservation_attendees_guest_id_index').on(
      t.guestId,
    ),

    // Status and role indexes
    attendeeRoleIndex: index(
      'event_reservation_attendees_attendee_role_index',
    ).on(t.attendeeRole),
    isVipIndex: index('event_reservation_attendees_is_vip_index').on(t.isVip),
    isConfirmedIndex: index(
      'event_reservation_attendees_is_confirmed_index',
    ).on(t.isConfirmed),
    rsvpStatusIndex: index('event_reservation_attendees_rsvp_status_index').on(
      t.rsvpStatus,
    ),
    checkedInIndex: index('event_reservation_attendees_checked_in_index').on(
      t.checkedIn,
    ),

    // Communication preferences indexes
    receiveUpdatesIndex: index(
      'event_reservation_attendees_receive_updates_index',
    ).on(t.receiveUpdates),

    // Plus-one indexes
    hasPlusOneIndex: index('event_reservation_attendees_has_plus_one_index').on(
      t.hasPlusOne,
    ),

    // Composite indexes for common queries
    reservationAttendeeRoleIndex: index(
      'event_reservation_attendees_reservation_role_index',
    ).on(t.reservationId, t.attendeeRole),
    reservationRsvpStatusIndex: index(
      'event_reservation_attendees_reservation_rsvp_index',
    ).on(t.reservationId, t.rsvpStatus),
    reservationCheckInIndex: index(
      'event_reservation_attendees_reservation_checkin_index',
    ).on(t.reservationId, t.checkedIn),
    guestEventStatusIndex: index(
      'event_reservation_attendees_guest_status_index',
    ).on(t.guestId, t.rsvpStatus),

    // Unique constraints
    uniqueReservationGuest: uniqueIndex(
      'event_reservation_attendees_unique',
    ).on(t.reservationId, t.guestId),
  }),
);

// Event Equipment Reservations table (for tracking specific equipment needs)
export const eventEquipmentReservations = pgTable(
  'event_equipment_reservations',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    reservationId: uuid('reservation_id')
      .notNull()
      .references(() => eventSpaceReservations.id, {
        onDelete: 'cascade',
      }),

    // Asset reference (replaces equipmentName and equipmentType)
    assetId: uuid('asset_id')
      .notNull()
      .references(() => assets.id),
    quantity: integer('quantity').default(1).notNull(),
    unitCost: decimal('unit_cost', { precision: 12, scale: 2 }).default('0.00'),
    totalCost: decimal('total_cost', { precision: 12, scale: 2 }).default(
      '0.00',
    ),

    // Timing
    setupTime: timestamp('setup_time'),
    readyTime: timestamp('ready_time'),
    breakdownTime: timestamp('breakdown_time'),

    // Status and notes
    status: text('status').default('REQUESTED').notNull(), // REQUESTED, CONFIRMED, SETUP, IN_USE, BREAKDOWN, RETURNED
    notes: text('notes'),
    supplier: text('supplier'), // Internal or external supplier
    confirmationNumber: text('confirmation_number'),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    reservationIdIndex: index(
      'event_equipment_reservations_reservation_id_index',
    ).on(t.reservationId),
    assetIdIndex: index(
      'event_equipment_reservations_asset_id_index',
    ).on(t.assetId),
    statusIndex: index('event_equipment_reservations_status_index').on(
      t.status,
    ),
    setupTimeIndex: index('event_equipment_reservations_setup_time_index').on(
      t.setupTime,
    ),
    supplierIndex: index('event_equipment_reservations_supplier_index').on(
      t.supplier,
    ),

    // Composite indexes
    reservationAssetIndex: index(
      'event_equipment_reservations_reservation_asset_index',
    ).on(t.reservationId, t.assetId),
    reservationStatusIndex: index(
      'event_equipment_reservations_reservation_status_index',
    ).on(t.reservationId, t.status),
  }),
);
